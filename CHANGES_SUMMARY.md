# Multiple API Tokens for Different Websites - Implementation Summary

## Overview
This implementation allows users to generate multiple API tokens for different websites, each with an optional label/website name for easy identification.

## Changes Made

### 1. Database Schema Updates
**File: `lhdn-e-invoice-receiver.php`**
- Added `website_name` column to the `leir_tokens` table
- Added database migration function `leir_migrate_database()` to handle existing installations
- Migration automatically adds the new column if it doesn't exist

### 2. Token Generation Functions
**File: `includes/helpers.php`**
- Updated `leir_generate_user_token()` function to accept optional `$website_name` parameter
- Added proper sanitization for website names
- Updated database insert to include website_name field

**File: `includes/api.php`**
- Updated `leir_get_user_tokens()` to select and return the `website_name` field
- API authentication (`leir_check_token_auth()`) continues to work unchanged

### 3. Admin Interface Updates
**File: `admin/class-token-manager.php`**
- Added website name input field in the token generation form
- Updated tokens display table to show website names as the first column
- Modified AJAX handler to accept and process website_name parameter
- Updated `render_tokens_table()` method to display website names
- Fixed colspan for "no tokens" message to match new column count

### 4. JavaScript Enhancements
**File: `admin/js/token-manager.js`**
- Updated token generation AJAX call to include website name from input field
- Added automatic clearing of website name input after successful token generation
- Enhanced user experience with proper form handling

## New Features

### User Interface
- **Website Name Input**: Users can now specify a label/website name when generating tokens
- **Enhanced Token Display**: Tokens are displayed in a table with columns for:
  - Website/Label (shows "(No label)" for tokens without names)
  - Token (the actual API token)
  - Created (creation date)
  - Actions (delete button)

### Database Structure
```sql
CREATE TABLE wp_leir_tokens (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    user_id bigint(20) NOT NULL,
    token varchar(255) NOT NULL,
    website_name varchar(255) DEFAULT '' NOT NULL,  -- NEW COLUMN
    created datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY token (token),
    KEY user_id (user_id)
);
```

### API Compatibility
- All existing API endpoints continue to work without changes
- Token authentication remains the same - website names are for user organization only
- Backward compatibility maintained for existing tokens (they will show "(No label)")

## Usage Instructions

### For Users
1. Navigate to user profile in WordPress admin
2. Scroll to "E-Invoice API Tokens" section
3. Enter a descriptive name in the "Website/Label" field (optional)
4. Click "Generate New Token"
5. The new token will appear in the table with the specified website name

### For Developers
```php
// Generate token without website name (backward compatible)
$token = leir_generate_user_token($user_id);

// Generate token with website name
$token = leir_generate_user_token($user_id, 'My Online Store');

// Get all tokens for a user (now includes website_name field)
$tokens = leir_get_user_tokens($user_id);
foreach ($tokens as $token) {
    echo $token->website_name; // Website name or empty string
    echo $token->token;        // The actual token
    echo $token->created;      // Creation date
}
```

## Migration Notes
- Existing installations will automatically receive the database update when the plugin is activated
- Existing tokens will continue to work and will display "(No label)" in the website name column
- No manual intervention required for existing users

## Testing
A test file `test-token-generation.php` has been created to verify the functionality. This file should be removed after testing.

## Files Modified
1. `lhdn-e-invoice-receiver.php` - Database schema and migration
2. `includes/helpers.php` - Token generation function
3. `includes/api.php` - Token retrieval function
4. `admin/class-token-manager.php` - Admin interface and AJAX handlers
5. `admin/js/token-manager.js` - Frontend JavaScript

## Security Considerations
- Website names are properly sanitized using `sanitize_text_field()`
- All existing security measures remain in place
- Token authentication is unchanged and secure
- Input validation prevents XSS and injection attacks
