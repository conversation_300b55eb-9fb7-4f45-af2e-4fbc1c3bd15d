# Instructions for Receiving Platform API (LHDN E-Invoice Integration)

This document outlines the requirements for an API endpoint designed to receive e-invoice data from the provided WooCommerce LHDN E-Invoice plugin.

## 1. Endpoint URL

*   Provide a stable, publicly accessible HTTPS URL for your API endpoint.
*   Example: `https://your-platform.com/api/receive-einvoice`
*   This URL will be configured within the WooCommerce plugin's settings.

## 2. HTTP Method

*   The endpoint **MUST** accept `POST` requests.
*   The plugin exclusively uses the `POST` method to send invoice data. Other methods like GET, PUT, DELETE are not required for this specific interaction.

## 3. Authentication

*   The endpoint **MUST** require and validate an `Authorization` HTTP header.
*   The value of this header will be a pre-shared secret key or token (e.g., `Bearer YOUR_SECRET_TOKEN`, or a custom scheme).
*   Provide this secret key to the users who will configure the WooCommerce plugin.
*   **Action:** Reject requests lacking a valid `Authorization` header with an HTTP `401 Unauthorized` status code.

## 4. Request Headers

*   Expect the `Content-Type` header to be `application/json`. The request body's format will always be JSON.

## 5. Request Body (Payload)

*   The request body will contain a **JSON Array**.
*   This array currently contains **one JSON object** representing a single invoice. Design your processing logic to handle the first (and currently only) element of this array.
*   The expected structure of the invoice object is:

    ```json
    [
      {
        "Edit": 1,
        "OrderID": 123,
        "TransactionCode": "WC-ORDER-123",
        "TransactionDate": "YYYY-MM-DD",
        "TransactionTime": "HH:MM:SS",
        "Category": "Retail",
        "BuyerName": "John Doe",
        "BuyerTinNo": "TIN123456",
        "BuyerIdType": "MyKad",
        "BuyerIdValue": "800101-12-1234",
        "BuyerSstNo": "SST98765",
        "BuyerEmail": "<EMAIL>",
        "BuyerAddress": "123 Main St, Apt 4B, Subang Jaya, Selangor, 47500, MY",
        "BuyerCityName": "Subang Jaya",
        "BuyerStateCode": "SEL",
        "BuyerCountryCode": "MY",
        "BuyerContactNo": "+60123456789",
        "TotalTaxAmount": 12.50,
        "TotalIncludingTax": 250.50,
        "TotalExcludingTax": 238.00,
        "TotalSubtotalAmount": 240.00,
        "TotalPayableAmount": 250.50,
        "TotalDiscount": 10.00,
        "Items": [
          {
            "ItemNo": 1,
            "ItemCode": "SKU123",
            "ItemDescription": "Product Name - Variation",
            "ItemQuantity": 2,
            "ItemUnitPrice": 120.00,
            "ItemTotalPrice": 230.00,
            "ItemTaxAmount": 12.50,
            "ItemTotalIncludingTax": 242.50,
            "ItemDiscount": 10.00
          }
        ],
        "Payments": [
          {
            "PaymentMethod": "Credit Card (Stripe)",
            "PaymentAmount": 250.50
          }
        ]
      }
    ]
    ```

*   **Field Notes:**
    *   `Edit`: Seems hardcoded to `1`.
    *   `OrderID`: The internal WooCommerce Order ID (Integer).
    *   `TransactionCode`: The customer-facing WooCommerce Order Number (String).
    *   `Category`: Hardcoded to `"Retail"`.
    *   Buyer fields (`BuyerTinNo`, `BuyerIdType`, `BuyerIdValue`, `BuyerSstNo`) can be empty strings if not provided by the customer.
    *   `BuyerAddress`: Concatenated string of address fields.
    *   All monetary values are numbers (potentially floats/decimals).
    *   `Items`: An array of line items. `ItemCode` might be SKU or `PROD` + Product ID. `ItemUnitPrice` is pre-discount, ex-tax. `ItemTotalPrice` is post-discount, ex-tax. `ItemTotalIncludingTax` is post-discount, inc-tax.
    *   `Payments`: The plugin currently sends one entry with the payment method title and the full order total.

## 6. Data Validation

*   Validate the structure of the incoming JSON array and the invoice object within it.
*   Check for the presence of essential fields (e.g., `TransactionCode`, `TransactionDate`, `TotalIncludingTax`). Define which fields are mandatory for your system.
*   Verify data types (string, number, array) and formats (e.g., `YYYY-MM-DD` for date).
*   **Action:** If validation fails, respond with an HTTP `400 Bad Request` status code. Include a clear error message in the JSON response body.

## 7. Processing Logic

*   After successful authentication and validation, process the received invoice data according to your platform's requirements (e.g., save to database, integrate with accounting system).

## 8. API Response

*   **On Success:**
    *   Respond with an HTTP `200 OK` status code.
    *   The response body should be JSON, confirming success. Include a reference ID if possible.
        ```json
        {
          "success": true,
          "message": "E-Invoice data received successfully.",
          "referenceId": "YOUR_INTERNAL_ID_123"
        }
        ```
*   **On Failure:**
    *   Use appropriate HTTP status codes:
        *   `400 Bad Request`: Invalid payload, missing data, validation errors.
        *   `401 Unauthorized`: Authentication failure (invalid/missing `Authorization` header).
        *   `403 Forbidden`: Authentication successful, but lacking permission (if applicable).
        *   `500 Internal Server Error`: An unexpected error occurred on your server during processing.
    *   The response body should be JSON, indicating failure and providing an error description.
        ```json
        {
          "success": false,
          "error": "Validation failed: Missing required field 'BuyerName'."
        }
        ```
        ```json
        {
          "success": false,
          "error": "Authentication failed."
        }
        ```
        ```json
        {
          "success": false,
          "error": "Internal server error occurred while saving invoice data."
        }
        ```

## 9. Idempotency (Recommended)

*   Consider implementing idempotency checks based on the `TransactionCode` (WC Order Number) or `OrderID` (WC Order ID).
*   If a duplicate submission for an already processed order is received, you can either:
    *   Return a success response (e.g., `200 OK`) perhaps with a specific message indicating it was a duplicate.
    *   Return a specific status code like `409 Conflict` (though `200 OK` might be simpler for the sending plugin).
*   This prevents issues if the user manually retries the submission via the plugin interface.