<?php
/**
 * Test script for token generation with website names
 * This file can be used to test the new functionality
 */

// This is a test file - remove after testing
if (!defined('ABSPATH')) {
    // For testing outside WordPress, you can uncomment the lines below
    // and adjust the path to wp-config.php
    // require_once('../../../wp-config.php');
    exit('This file should be run within WordPress context');
}

/**
 * Test function to verify token generation with website names
 */
function test_leir_token_generation() {
    // Test user ID (use a valid user ID from your WordPress installation)
    $test_user_id = 1; // Usually the admin user
    
    echo "<h2>Testing LEIR Token Generation with Website Names</h2>";
    
    // Test 1: Generate token without website name
    echo "<h3>Test 1: Generate token without website name</h3>";
    $token1 = leir_generate_user_token($test_user_id);
    if ($token1) {
        echo "✅ Token generated successfully: " . substr($token1, 0, 10) . "...<br>";
    } else {
        echo "❌ Failed to generate token<br>";
    }
    
    // Test 2: Generate token with website name
    echo "<h3>Test 2: Generate token with website name</h3>";
    $token2 = leir_generate_user_token($test_user_id, "My Online Store");
    if ($token2) {
        echo "✅ Token generated successfully: " . substr($token2, 0, 10) . "...<br>";
    } else {
        echo "❌ Failed to generate token<br>";
    }
    
    // Test 3: Generate another token with different website name
    echo "<h3>Test 3: Generate token with different website name</h3>";
    $token3 = leir_generate_user_token($test_user_id, "Company Website");
    if ($token3) {
        echo "✅ Token generated successfully: " . substr($token3, 0, 10) . "...<br>";
    } else {
        echo "❌ Failed to generate token<br>";
    }
    
    // Test 4: Retrieve all tokens for the user
    echo "<h3>Test 4: Retrieve all tokens for user</h3>";
    $tokens = leir_get_user_tokens($test_user_id);
    if (!empty($tokens)) {
        echo "✅ Retrieved " . count($tokens) . " tokens:<br>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Website Name</th><th>Token (first 10 chars)</th><th>Created</th></tr>";
        foreach ($tokens as $token) {
            echo "<tr>";
            echo "<td>" . esc_html($token->website_name ?: '(No label)') . "</td>";
            echo "<td>" . esc_html(substr($token->token, 0, 10)) . "...</td>";
            echo "<td>" . esc_html($token->created) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No tokens found<br>";
    }
    
    // Test 5: Test API authentication with one of the tokens
    echo "<h3>Test 5: Test API authentication</h3>";
    if (!empty($tokens)) {
        $test_token = $tokens[0]->token;
        
        // Simulate a request object
        $request = new WP_REST_Request();
        $request->set_header('Authorization', 'Bearer ' . $test_token);
        
        $auth_result = leir_check_token_auth($request);
        if (!is_wp_error($auth_result)) {
            echo "✅ Token authentication successful<br>";
        } else {
            echo "❌ Token authentication failed: " . $auth_result->get_error_message() . "<br>";
        }
    }
    
    echo "<h3>Test completed!</h3>";
    echo "<p><strong>Note:</strong> This test file should be removed after testing.</p>";
}

// Run the test if this file is accessed directly
if (isset($_GET['run_test']) && $_GET['run_test'] === '1') {
    test_leir_token_generation();
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>LEIR Token Generation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; }
        th, td { padding: 8px; border: 1px solid #ddd; }
        th { background-color: #f5f5f5; }
    </style>
</head>
<body>
    <h1>LEIR Token Generation Test</h1>
    <p>This test will verify that the new website name functionality works correctly.</p>
    <p><a href="?run_test=1">Click here to run the test</a></p>
    
    <?php if (isset($_GET['run_test']) && $_GET['run_test'] === '1'): ?>
        <hr>
        <?php test_leir_token_generation(); ?>
    <?php endif; ?>
</body>
</html>
