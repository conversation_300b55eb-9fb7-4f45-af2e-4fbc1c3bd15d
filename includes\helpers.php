<?php
/**
 * Helper functions for E-Invoice Receiver
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Check if current user can manage e-invoice settings
 *
 * @return bool Whether the current user has sufficient permissions
 */
function leir_current_user_can_manage() {
    return current_user_can('manage_options');
}

/**
 * Format a date for display
 *
 * @param string $date The date string
 * @param string $format The format to use (defaults to site format)
 * @return string The formatted date
 */
function leir_format_date($date, $format = '') {
    if (empty($format)) {
        $format = get_option('date_format') . ' ' . get_option('time_format');
    }
    
    $datetime = new DateTime($date);
    return $datetime->format($format);
}

/**
 * Check if debug mode is enabled
 *
 * @return bool Whether debug mode is enabled
 */
function leir_is_debug_mode() {
    return (bool) apply_filters('leir_debug_mode', false);
}

/**
 * Log debug information if debug mode is enabled
 *
 * @param mixed $data The data to log
 * @param string $title Optional title for the log entry
 * @return void
 */
function leir_debug_log($data, $title = '') {
    if (!leir_is_debug_mode()) {
        return;
    }
    
    $log_file = LEIR_PLUGIN_DIR . 'debug.log';
    
    $log_entry = '[' . current_time('mysql') . '] ';
    if (!empty($title)) {
        $log_entry .= $title . ': ';
    }
    
    if (is_array($data) || is_object($data)) {
        $log_entry .= print_r($data, true);
    } else {
        $log_entry .= $data;
    }
    
    $log_entry .= "\n";
    
    error_log($log_entry, 3, $log_file);
}

/**
 * Generate help text for the API endpoint
 *
 * @return string The formatted help text
 */
function leir_get_api_help_text() {
    $endpoint_url = leir_get_api_endpoint_url();
    
    $text = '<h4>' . __('API Endpoint Information', 'lhdn-e-invoice-receiver') . '</h4>';
    $text .= '<p><strong>' . __('Endpoint URL:', 'lhdn-e-invoice-receiver') . '</strong> ' . $endpoint_url . '</p>';
    $text .= '<p><strong>' . __('Method:', 'lhdn-e-invoice-receiver') . '</strong> POST</p>';
    $text .= '<p><strong>' . __('Headers:', 'lhdn-e-invoice-receiver') . '</strong></p>';
    $text .= '<ul>';
    $text .= '<li><code>Content-Type: application/json</code></li>';
    $text .= '<li><code>Authorization: Bearer YOUR_TOKEN</code> ' . __('(Replace YOUR_TOKEN with your actual token)', 'lhdn-e-invoice-receiver') . '</li>';
    $text .= '</ul>';
    $text .= '<p><strong>' . __('Body:', 'lhdn-e-invoice-receiver') . '</strong> ' . __('JSON array of invoice objects (see documentation)', 'lhdn-e-invoice-receiver') . '</p>';
    
    return $text;
}

/**
 * Get a list of all invoices for a user
 *
 * @param int $user_id The user ID
 * @param int $limit The maximum number of invoices to retrieve
 * @param int $offset The offset for pagination
 * @return array The invoices
 */
function leir_get_user_invoices($user_id, $limit = 20, $offset = 0) {
    global $wpdb;
    
    // If user is admin, get all invoices
    if (current_user_can('manage_options')) {
        return $wpdb->get_results(
            $wpdb->prepare(
                "SELECT id, user_id, transaction_code, order_id, received_date 
                FROM {$wpdb->prefix}leir_invoices 
                ORDER BY received_date DESC
                LIMIT %d OFFSET %d",
                $limit,
                $offset
            )
        );
    }
    
    // For non-admin users, only get their own invoices
    return $wpdb->get_results(
        $wpdb->prepare(
            "SELECT id, user_id, transaction_code, order_id, received_date 
            FROM {$wpdb->prefix}leir_invoices 
            WHERE user_id = %d
            ORDER BY received_date DESC
            LIMIT %d OFFSET %d",
            $user_id,
            $limit,
            $offset
        )
    );
}

/**
 * Get a single invoice by ID
 *
 * @param int $invoice_id The invoice ID
 * @param int $user_id The user ID (optional, defaults to current user)
 * @return object|null The invoice or null if not found
 */
function leir_get_invoice($invoice_id, $user_id = null) {
    global $wpdb;
    
    if ($user_id === null) {
        $user_id = get_current_user_id();
    }
    
    // If user is admin, don't check user_id
    if (current_user_can('manage_options')) {
        $invoice = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}leir_invoices WHERE id = %d",
                $invoice_id
            )
        );
    } else {
        $invoice = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}leir_invoices WHERE id = %d AND user_id = %d",
                $invoice_id,
                $user_id
            )
        );
    }
    
    if ($invoice) {
        $invoice->invoice_data = json_decode($invoice->invoice_data, true);
    }
    
    return $invoice;
}

/**
 * Count the total number of invoices for a user
 *
 * @param int $user_id The user ID
 * @return int The total number of invoices
 */
function leir_count_user_invoices($user_id) {
    global $wpdb;
    
    return (int) $wpdb->get_var(
        $wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}leir_invoices WHERE user_id = %d",
            $user_id
        )
    );
}

/**
 * Generate a unique token for a user
 *
 * @param int $user_id The user ID
 * @param string $website_name Optional website name/label for the token
 * @return string|false The generated token or false on failure
 */
function leir_generate_user_token($user_id, $website_name = '') {
    if (empty($user_id) || !is_numeric($user_id)) {
        error_log('Invalid user ID for token generation: ' . var_export($user_id, true));
        return false;
    }

    // Sanitize website name
    $website_name = sanitize_text_field($website_name);

    // Generate a random token
    $token = wp_generate_password(32, false);

    // Store the token in the database
    global $wpdb;
    $result = $wpdb->insert(
        $wpdb->prefix . 'leir_tokens',
        array(
            'user_id' => $user_id,
            'token' => $token,
            'website_name' => $website_name,
            'created' => current_time('mysql')
        ),
        array(
            '%d',
            '%s',
            '%s',
            '%s'
        )
    );
    
    if ($result === false) {
        error_log('Database error when inserting token: ' . $wpdb->last_error);
        return false;
    }
    
    // Verify the token was stored correctly
    $verify = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT token FROM {$wpdb->prefix}leir_tokens WHERE token = %s AND user_id = %d",
            $token,
            $user_id
        )
    );
    
    if (!$verify) {
        error_log('Token verification failed after insert. Token not found in database.');
        return false;
    }
    
    return $token;
}

/**
 * Check if a user has an active subscription
 *
 * @param int $user_id The user ID
 * @return bool Whether the user has an active subscription
 */
function leir_user_has_active_subscription($user_id) {
    if (empty($user_id)) {
        return false;
    }
    
    $args = array(
        'post_type' => 'leir_subscription',
        'posts_per_page' => 1,
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key' => '_leir_user_id',
                'value' => $user_id,
                'compare' => '='
            ),
            array(
                'key' => '_leir_status',
                'value' => 'active',
                'compare' => '='
            ),
            array(
                'key' => '_leir_expiry_date',
                'value' => current_time('mysql'),
                'compare' => '>',
                'type' => 'DATETIME'
            )
        )
    );
    
    $subscription_query = new WP_Query($args);
    
    return $subscription_query->have_posts();
}

/**
 * Get a user's active subscription
 *
 * @param int $user_id The user ID
 * @return object|null The subscription post object or null if none found
 */
function leir_get_user_active_subscription($user_id) {
    if (empty($user_id)) {
        return null;
    }
    
    $args = array(
        'post_type' => 'leir_subscription',
        'posts_per_page' => 1,
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key' => '_leir_user_id',
                'value' => $user_id,
                'compare' => '='
            ),
            array(
                'key' => '_leir_status',
                'value' => 'active',
                'compare' => '='
            ),
            array(
                'key' => '_leir_expiry_date',
                'value' => current_time('mysql'),
                'compare' => '>',
                'type' => 'DATETIME'
            )
        )
    );
    
    $subscription_query = new WP_Query($args);
    
    if ($subscription_query->have_posts()) {
        return $subscription_query->posts[0];
    }
    
    return null;
}

/**
 * Get subscription expiry date for a user
 *
 * @param int $user_id The user ID
 * @return string|null The expiry date or null if no active subscription
 */
function leir_get_subscription_expiry($user_id) {
    $subscription = leir_get_user_active_subscription($user_id);
    
    if ($subscription) {
        return get_post_meta($subscription->ID, '_leir_expiry_date', true);
    }
    
    return null;
}

/**
 * Check if a subscription is about to expire (within 30 days)
 *
 * @param int $subscription_id The subscription ID
 * @return bool Whether the subscription is about to expire
 */
function leir_is_subscription_expiring_soon($subscription_id) {
    $expiry_date = get_post_meta($subscription_id, '_leir_expiry_date', true);
    $status = get_post_meta($subscription_id, '_leir_status', true);
    
    if ($status !== 'active' || empty($expiry_date)) {
        return false;
    }
    
    $expiry_timestamp = strtotime($expiry_date);
    $now = time();
    $days_remaining = ($expiry_timestamp - $now) / DAY_IN_SECONDS;
    
    return $days_remaining <= 30 && $days_remaining > 0;
} 