/**
 * Token Manager JavaScript
 */
(function($) {
    'use strict';
    
    $(document).ready(function() {
        console.log('Token manager JS loaded');
        
        // Generate token
        $('.leir-generate-token').on('click', function(e) {
            e.preventDefault();
            console.log('Generate token button clicked');

            var button = $(this);
            var userId = button.data('user-id');
            var websiteName = $('#leir-website-name').val().trim();
            var messageSpan = button.siblings('.leir-token-message');
            var container = $('#leir-tokens-container');

            console.log('Button clicked for user ID:', userId);
            console.log('Website name:', websiteName);
            console.log('Ajax URL:', leirTokenManager.ajaxUrl);
            console.log('Generate Nonce:', leirTokenManager.generateNonce);

            $.ajax({
                url: leirTokenManager.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'leir_generate_token',
                    user_id: userId,
                    website_name: websiteName,
                    nonce: leirTokenManager.generateNonce
                },
                beforeSend: function() {
                    button.prop('disabled', true);
                    messageSpan.text(leirTokenManager.generating);
                },
                success: function(response) {
                    console.log('AJAX response:', response);

                    if (response.success && response.data && response.data.html) {
                        container.html(response.data.html);
                        messageSpan.text(response.data.message || 'Token generated successfully.');
                        // Clear the website name input after successful generation
                        $('#leir-website-name').val('');
                        setTimeout(function() {
                            messageSpan.text('');
                        }, 3000);
                    } else {
                        messageSpan.text(response.data ? response.data.message : leirTokenManager.error);
                    }
                    button.prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    console.error('Ajax error:', status, error);
                    console.error('Response Text:', xhr.responseText);
                    console.error('Status Code:', xhr.status);
                    
                    var errorMessage = leirTokenManager.error;
                    
                    // Try to parse the error response
                    try {
                        var errorData = JSON.parse(xhr.responseText);
                        if (errorData && errorData.data && errorData.data.message) {
                            errorMessage = errorData.data.message;
                        }
                    } catch (parseError) {
                        // If it's not JSON, check if it's an HTML error page
                        if (xhr.responseText.indexOf('<div') === 0 || xhr.responseText.indexOf('<!DOCTYPE') === 0) {
                            errorMessage = 'Server returned an error page instead of JSON. Please check server logs.';
                        }
                    }
                    
                    messageSpan.text(errorMessage);
                    button.prop('disabled', false);
                }
            });
        });
        
        // Delete token (delegated event for dynamically added elements)
        $(document).on('click', '.leir-delete-token', function(e) {
            e.preventDefault();
            console.log('Delete token link clicked');
            
            if (!confirm(leirTokenManager.confirmDelete)) {
                return;
            }
            
            var link = $(this);
            var token = link.data('token');
            var userId = link.data('user-id');
            var container = $('#leir-tokens-container');
            var messageSpan = $('.leir-token-message');
            
            console.log('Deleting token for user ID:', userId);
            
            $.ajax({
                url: leirTokenManager.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'leir_delete_token',
                    token: token,
                    user_id: userId,
                    nonce: leirTokenManager.deleteNonce
                },
                beforeSend: function() {
                    messageSpan.text('');
                },
                success: function(response) {
                    console.log('AJAX response:', response);
                    
                    if (response.success && response.data && response.data.html) {
                        container.html(response.data.html);
                        messageSpan.text(response.data.message || 'Token deleted successfully.');
                        setTimeout(function() {
                            messageSpan.text('');
                        }, 3000);
                    } else {
                        messageSpan.text(response.data ? response.data.message : leirTokenManager.error);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Ajax error:', status, error);
                    console.error('Response Text:', xhr.responseText);
                    console.error('Status Code:', xhr.status);
                    
                    var errorMessage = leirTokenManager.error;
                    
                    // Try to parse the error response
                    try {
                        var errorData = JSON.parse(xhr.responseText);
                        if (errorData && errorData.data && errorData.data.message) {
                            errorMessage = errorData.data.message;
                        }
                    } catch (parseError) {
                        // If it's not JSON, check if it's an HTML error page
                        if (xhr.responseText.indexOf('<div') === 0 || xhr.responseText.indexOf('<!DOCTYPE') === 0) {
                            errorMessage = 'Server returned an error page instead of JSON. Please check server logs.';
                        }
                    }
                    
                    messageSpan.text(errorMessage);
                }
            });
        });
    });
})(jQuery); 