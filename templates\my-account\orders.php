<?php
/**
 * My Account Orders Template
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

?>

<div class="leir-my-account-orders">
    <h2><?php _e('Orders', 'lhdn-e-invoice-receiver'); ?></h2>
    
    <?php
    // Check if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        echo '<p>' . __('WooCommerce is not active. Orders functionality is not available.', 'lhdn-e-invoice-receiver') . '</p>';
        return;
    }

    // Get current page for pagination
    $current_page = max(1, get_query_var('paged'));
    $per_page = 20;

    // Get customer orders
    $customer_orders = wc_get_orders(array(
        'customer' => get_current_user_id(),
        'limit' => $per_page,
        'offset' => ($current_page - 1) * $per_page,
        'orderby' => 'date',
        'order' => 'DESC',
    ));

    // Get total count for pagination
    $total_orders = wc_get_orders(array(
        'customer' => get_current_user_id(),
        'limit' => -1,
        'return' => 'ids',
    ));
    $total_count = count($total_orders);

    if (!empty($customer_orders)) {
        ?>
        <div class="leir-orders-table-wrapper">
            <table class="leir-orders-table">
                <thead>
                    <tr>
                        <th><?php _e('Order', 'lhdn-e-invoice-receiver'); ?></th>
                        <th><?php _e('Date', 'lhdn-e-invoice-receiver'); ?></th>
                        <th><?php _e('Status', 'lhdn-e-invoice-receiver'); ?></th>
                        <th><?php _e('Total', 'lhdn-e-invoice-receiver'); ?></th>
                        <th><?php _e('Actions', 'lhdn-e-invoice-receiver'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    foreach ($customer_orders as $order) {
                        $order_id = $order->get_id();
                        $order_number = $order->get_order_number();
                        $order_date = $order->get_date_created();
                        $order_status = $order->get_status();
                        $order_total = $order->get_formatted_order_total();
                        
                        echo '<tr>';
                        echo '<td><strong>#' . esc_html($order_number) . '</strong></td>';
                        echo '<td>' . esc_html(wc_format_datetime($order_date)) . '</td>';
                        echo '<td><span class="order-status status-' . esc_attr($order_status) . '">' . esc_html(wc_get_order_status_name($order_status)) . '</span></td>';
                        echo '<td>' . wp_kses_post($order_total) . '</td>';
                        echo '<td>';
                        
                        // View order button
                        echo '<a href="' . esc_url($order->get_view_order_url()) . '" class="button view-order">' . __('View', 'lhdn-e-invoice-receiver') . '</a>';
                        
                        // Pay button for pending payment orders
                        if ($order->needs_payment()) {
                            echo ' <a href="' . esc_url($order->get_checkout_payment_url()) . '" class="button pay-order">' . __('Pay', 'lhdn-e-invoice-receiver') . '</a>';
                        }
                        
                        // Cancel button for pending orders
                        if ($order->get_status() === 'pending' || $order->get_status() === 'on-hold') {
                            echo ' <a href="' . esc_url($order->get_cancel_order_url(wc_get_page_permalink('myaccount'))) . '" class="button cancel-order" onclick="return confirm(\'' . __('Are you sure you want to cancel this order?', 'lhdn-e-invoice-receiver') . '\')">' . __('Cancel', 'lhdn-e-invoice-receiver') . '</a>';
                        }
                        
                        echo '</td>';
                        echo '</tr>';
                    }
                    ?>
                </tbody>
            </table>
        </div>

        <?php
        // Pagination
        if ($total_count > $per_page) {
            $total_pages = ceil($total_count / $per_page);
            
            echo '<div class="leir-pagination">';
            echo paginate_links(array(
                'base' => add_query_arg('paged', '%#%'),
                'format' => '',
                'current' => $current_page,
                'total' => $total_pages,
                'prev_text' => __('&laquo; Previous', 'lhdn-e-invoice-receiver'),
                'next_text' => __('Next &raquo;', 'lhdn-e-invoice-receiver'),
            ));
            echo '</div>';
        }
        ?>

    <?php
    } else {
        ?>
        <div class="leir-no-orders">
            <p><?php _e('No orders have been made yet.', 'lhdn-e-invoice-receiver'); ?></p>
            <?php if (wc_get_page_id('shop') > 0) : ?>
                <a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>" class="button">
                    <?php _e('Browse products', 'lhdn-e-invoice-receiver'); ?>
                </a>
            <?php endif; ?>
        </div>
        <?php
    }
    ?>
</div> 