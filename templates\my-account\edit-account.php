<?php
/**
 * My Account Edit Account Template
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

$current_user = wp_get_current_user();
$user_id = get_current_user_id();

// Handle form submission
if (isset($_POST['save_account_details']) && wp_verify_nonce($_POST['save_account_details_nonce'], 'save_account_details')) {
    $errors = array();
    
    // Get form data
    $first_name = sanitize_text_field($_POST['account_first_name']);
    $last_name = sanitize_text_field($_POST['account_last_name']);
    $display_name = sanitize_text_field($_POST['account_display_name']);
    $email = sanitize_email($_POST['account_email']);
    $current_password = $_POST['password_current'];
    $new_password = $_POST['password_1'];
    $confirm_password = $_POST['password_2'];
    
    // Validate email
    if (empty($email) || !is_email($email)) {
        $errors[] = __('Please provide a valid email address.', 'lhdn-e-invoice-receiver');
    } elseif (email_exists($email) && $email !== $current_user->user_email) {
        $errors[] = __('This email address is already registered.', 'lhdn-e-invoice-receiver');
    }
    
    // Validate password change
    if (!empty($new_password)) {
        if (empty($current_password)) {
            $errors[] = __('Please enter your current password.', 'lhdn-e-invoice-receiver');
        } elseif (!wp_check_password($current_password, $current_user->user_pass, $user_id)) {
            $errors[] = __('Your current password is incorrect.', 'lhdn-e-invoice-receiver');
        } elseif (empty($confirm_password)) {
            $errors[] = __('Please re-enter your password.', 'lhdn-e-invoice-receiver');
        } elseif ($new_password !== $confirm_password) {
            $errors[] = __('Passwords do not match.', 'lhdn-e-invoice-receiver');
        } elseif (strlen($new_password) < 8) {
            $errors[] = __('Password must be at least 8 characters long.', 'lhdn-e-invoice-receiver');
        }
    }
    
    // If no errors, update user data
    if (empty($errors)) {
        $user_data = array(
            'ID' => $user_id,
            'first_name' => $first_name,
            'last_name' => $last_name,
            'display_name' => $display_name,
            'user_email' => $email,
        );
        
        // Add password if provided
        if (!empty($new_password)) {
            $user_data['user_pass'] = $new_password;
        }
        
        $updated = wp_update_user($user_data);
        
        if (is_wp_error($updated)) {
            $errors[] = $updated->get_error_message();
        } else {
            echo '<div class="leir-notice leir-notice-success"><p>' . __('Account details changed successfully.', 'lhdn-e-invoice-receiver') . '</p></div>';
            // Refresh user data
            $current_user = wp_get_current_user();
        }
    }
    
    // Display errors
    if (!empty($errors)) {
        echo '<div class="leir-notice leir-notice-error">';
        foreach ($errors as $error) {
            echo '<p>' . esc_html($error) . '</p>';
        }
        echo '</div>';
    }
}

?>

<div class="leir-my-account-edit-account">
    <h2><?php _e('Account details', 'lhdn-e-invoice-receiver'); ?></h2>
    
    <form class="leir-edit-account-form" action="" method="post">
        
        <div class="leir-form-row">
            <div class="leir-form-group leir-form-group-wide">
                <label for="account_first_name"><?php _e('First name', 'lhdn-e-invoice-receiver'); ?> <span class="required">*</span></label>
                <input type="text" class="leir-form-control" name="account_first_name" id="account_first_name" value="<?php echo esc_attr($current_user->first_name); ?>" required />
            </div>
            <div class="leir-form-group leir-form-group-wide">
                <label for="account_last_name"><?php _e('Last name', 'lhdn-e-invoice-receiver'); ?> <span class="required">*</span></label>
                <input type="text" class="leir-form-control" name="account_last_name" id="account_last_name" value="<?php echo esc_attr($current_user->last_name); ?>" required />
            </div>
        </div>
        
        <div class="leir-form-row">
            <div class="leir-form-group leir-form-group-wide">
                <label for="account_display_name"><?php _e('Display name', 'lhdn-e-invoice-receiver'); ?> <span class="required">*</span></label>
                <input type="text" class="leir-form-control" name="account_display_name" id="account_display_name" value="<?php echo esc_attr($current_user->display_name); ?>" required />
                <small><?php _e('This will be how your name will be displayed in the account section and in reviews', 'lhdn-e-invoice-receiver'); ?></small>
            </div>
        </div>
        
        <div class="leir-form-row">
            <div class="leir-form-group leir-form-group-wide">
                <label for="account_email"><?php _e('Email address', 'lhdn-e-invoice-receiver'); ?> <span class="required">*</span></label>
                <input type="email" class="leir-form-control" name="account_email" id="account_email" value="<?php echo esc_attr($current_user->user_email); ?>" required />
            </div>
        </div>
        
        <fieldset class="leir-password-fieldset">
            <legend><?php _e('Password change', 'lhdn-e-invoice-receiver'); ?></legend>
            
            <div class="leir-form-row">
                <div class="leir-form-group leir-form-group-wide">
                    <label for="password_current"><?php _e('Current password (leave blank to leave unchanged)', 'lhdn-e-invoice-receiver'); ?></label>
                    <input type="password" class="leir-form-control" name="password_current" id="password_current" autocomplete="off" />
                </div>
            </div>
            
            <div class="leir-form-row">
                <div class="leir-form-group leir-form-group-wide">
                    <label for="password_1"><?php _e('New password (leave blank to leave unchanged)', 'lhdn-e-invoice-receiver'); ?></label>
                    <input type="password" class="leir-form-control" name="password_1" id="password_1" autocomplete="off" />
                </div>
            </div>
            
            <div class="leir-form-row">
                <div class="leir-form-group leir-form-group-wide">
                    <label for="password_2"><?php _e('Confirm new password', 'lhdn-e-invoice-receiver'); ?></label>
                    <input type="password" class="leir-form-control" name="password_2" id="password_2" autocomplete="off" />
                </div>
            </div>
        </fieldset>
        
        <div class="leir-form-row">
            <div class="leir-form-group">
                <?php wp_nonce_field('save_account_details', 'save_account_details_nonce'); ?>
                <button type="submit" class="leir-button button" name="save_account_details" value="<?php esc_attr_e('Save changes', 'lhdn-e-invoice-receiver'); ?>">
                    <?php _e('Save changes', 'lhdn-e-invoice-receiver'); ?>
                </button>
            </div>
        </div>
        
    </form>
</div> 