<?php
/**
 * Public-facing functionality
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class for handling public-facing functionality
 */
class LEIR_Public {
    /**
     * Constructor
     */
    public function __construct() {
        // Add endpoint
        add_action('init', array($this, 'add_einvoice_endpoint'));
        
        // WooCommerce My Account integration
        add_action('init', array($this, 'init_woocommerce_integration'));
    }

    /**
     * Initialize WooCommerce integration
     */
    public function init_woocommerce_integration() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            return;
        }

        // Add new tab to My Account page
        add_filter('woocommerce_account_menu_items', array($this, 'add_einvoice_tab'));
        
        // Add content to the new tab
        add_action('woocommerce_account_e-invoice_endpoint', array($this, 'einvoice_tab_content'));
    }

    /**
     * Add E-Invoice endpoint
     */
    public function add_einvoice_endpoint() {
        add_rewrite_endpoint('e-invoice', EP_ROOT | EP_PAGES);
    }

    /**
     * Add E-Invoice tab to My Account menu
     *
     * @param array $items Menu items
     * @return array Modified menu items
     */
    public function add_einvoice_tab($items) {
        // Insert the E-Invoice tab before the logout menu item
        $logout_item = false;
        
        if (isset($items['customer-logout'])) {
            $logout_item = $items['customer-logout'];
            unset($items['customer-logout']);
        }
        
        // Add our new item
        $items['e-invoice'] = __('E-Invoice', 'lhdn-e-invoice-receiver');
        
        // Re-add logout item at the end if it exists
        if ($logout_item) {
            $items['customer-logout'] = $logout_item;
        }
        
        return $items;
    }

    /**
     * E-Invoice tab content
     */
    public function einvoice_tab_content() {
        // Get current user's invoices
        $user_id = get_current_user_id();
        
        // Check if user has an active subscription
        if (!leir_user_has_active_subscription($user_id) && !current_user_can('manage_options')) {
            $this->display_subscription_required();
            return;
        }
        
        // Set up pagination
        $per_page = 10;
        $current_page = max(1, get_query_var('paged'));
        $offset = ($current_page - 1) * $per_page;
        
        $invoices = leir_get_user_invoices($user_id, $per_page, $offset);
        $total_invoices = leir_count_user_invoices($user_id);
        
        // Check if we're viewing a single invoice
        $invoice_id = isset($_GET['invoice_id']) ? intval($_GET['invoice_id']) : 0;
        
        if ($invoice_id > 0) {
            $invoice = leir_get_invoice($invoice_id, $user_id);
            if ($invoice) {
                $this->display_single_invoice($invoice);
                return;
            }
        }
        
        // Display invoices list
        $this->display_invoices_list($invoices, $total_invoices, $per_page, $current_page);
    }
    
    /**
     * Display subscription required message
     */
    private function display_subscription_required() {
        ?>
        <div class="woocommerce-message woocommerce-message--info woocommerce-Message woocommerce-Message--info woocommerce-info">
            <h2><?php _e('Subscription Required', 'lhdn-e-invoice-receiver'); ?></h2>
            <p><?php _e('You need an active subscription to access E-Invoice features.', 'lhdn-e-invoice-receiver'); ?></p>
            
            <?php
            // Get subscription products
            $args = array(
                'post_type' => 'product',
                'posts_per_page' => -1,
                'meta_key' => '_leir_is_subscription',
                'meta_value' => '1'
            );
            
            $subscription_products = get_posts($args);
            
            if (!empty($subscription_products)) {
                echo '<h3>' . __('Available Subscription Plans', 'lhdn-e-invoice-receiver') . '</h3>';
                echo '<ul class="subscription-plans">';
                
                foreach ($subscription_products as $product) {
                    $wc_product = wc_get_product($product->ID);
                    $duration = get_post_meta($product->ID, '_leir_subscription_duration', true);
                    
                    if ($wc_product) {
                        echo '<li>';
                        echo '<h4>' . esc_html($wc_product->get_name()) . '</h4>';
                        echo '<p class="price">' . $wc_product->get_price_html() . '</p>';
                        echo '<p class="duration">' . sprintf(_n('%s month', '%s months', $duration, 'lhdn-e-invoice-receiver'), $duration) . '</p>';
                        echo '<a href="' . esc_url($wc_product->get_permalink()) . '" class="button">' . __('View Plan', 'lhdn-e-invoice-receiver') . '</a>';
                        echo '</li>';
                    }
                }
                
                echo '</ul>';
            }
            ?>
            
            <p>
                <a class="button" href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>">
                    <?php _e('Browse Subscription Plans', 'lhdn-e-invoice-receiver'); ?>
                </a>
            </p>
        </div>
        <?php
    }

    /**
     * Display a list of invoices
     *
     * @param array $invoices The invoices to display
     * @param int $total_invoices Total invoice count for pagination
     * @param int $per_page Number of items per page
     * @param int $current_page Current page number
     */
    private function display_invoices_list($invoices, $total_invoices, $per_page, $current_page) {
        ?>
        <h2><?php _e('My E-Invoices', 'lhdn-e-invoice-receiver'); ?></h2>
        
        <?php if (empty($invoices)) : ?>
            <p><?php _e('You don\'t have any e-invoices yet.', 'lhdn-e-invoice-receiver'); ?></p>
        <?php else : ?>
            <table class="woocommerce-orders-table woocommerce-MyAccount-orders shop_table shop_table_responsive my_account_orders account-orders-table">
                <thead>
                    <tr>
                        <th><?php _e('Invoice ID', 'lhdn-e-invoice-receiver'); ?></th>
                        <th><?php _e('Transaction Code', 'lhdn-e-invoice-receiver'); ?></th>
                        <th><?php _e('Order', 'lhdn-e-invoice-receiver'); ?></th>
                        <th><?php _e('Date', 'lhdn-e-invoice-receiver'); ?></th>
                        <th><?php _e('Actions', 'lhdn-e-invoice-receiver'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($invoices as $invoice) : ?>
                        <tr>
                            <td>
                                <?php echo esc_html($invoice->id); ?>
                            </td>
                            <td>
                                <?php echo esc_html($invoice->transaction_code); ?>
                            </td>
                            <td>
                                <?php 
                                if (!empty($invoice->order_id)) {
                                    echo '<a href="' . esc_url(wc_get_account_endpoint_url('view-order') . $invoice->order_id) . '">#' . esc_html($invoice->order_id) . '</a>';
                                } else {
                                    echo '--';
                                }
                                ?>
                            </td>
                            <td>
                                <?php echo esc_html(leir_format_date($invoice->received_date)); ?>
                            </td>
                            <td>
                                <a href="<?php echo esc_url(add_query_arg('invoice_id', $invoice->id, wc_get_account_endpoint_url('e-invoice'))); ?>" class="woocommerce-button button view">
                                    <?php _e('View', 'lhdn-e-invoice-receiver'); ?>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <?php
            // Pagination
            if ($total_invoices > $per_page) {
                $total_pages = ceil($total_invoices / $per_page);
                
                echo '<div class="woocommerce-pagination woocommerce-pagination--without-numbers woocommerce-Pagination">';
                
                if ($current_page > 1) {
                    echo '<a class="woocommerce-button woocommerce-button--previous woocommerce-Button woocommerce-Button--previous button" href="' . esc_url(wc_get_account_endpoint_url('e-invoice') . 'page/' . ($current_page - 1)) . '">' . __('Previous', 'lhdn-e-invoice-receiver') . '</a>';
                }
                
                if ($current_page < $total_pages) {
                    echo '<a class="woocommerce-button woocommerce-button--next woocommerce-Button woocommerce-Button--next button" href="' . esc_url(wc_get_account_endpoint_url('e-invoice') . 'page/' . ($current_page + 1)) . '">' . __('Next', 'lhdn-e-invoice-receiver') . '</a>';
                }
                
                echo '</div>';
            }
            ?>
        <?php endif; ?>
        <?php
    }

    /**
     * Display a single invoice
     *
     * @param object $invoice The invoice to display
     */
    private function display_single_invoice($invoice) {
        $invoice_data = $invoice->invoice_data;
        ?>
        <div class="leir-invoice-view">
            <p>
                <a href="<?php echo esc_url(wc_get_account_endpoint_url('e-invoice')); ?>" class="button">
                    <?php _e('← Back to E-Invoices', 'lhdn-e-invoice-receiver'); ?>
                </a>
            </p>
            
            <h2><?php _e('E-Invoice Details', 'lhdn-e-invoice-receiver'); ?></h2>
            
            <div class="leir-invoice-header">
                <div class="leir-invoice-meta">
                    <p><strong><?php _e('Invoice ID:', 'lhdn-e-invoice-receiver'); ?></strong> <?php echo esc_html($invoice->id); ?></p>
                    <p><strong><?php _e('Transaction Code:', 'lhdn-e-invoice-receiver'); ?></strong> <?php echo esc_html($invoice->transaction_code); ?></p>
                    <p><strong><?php _e('Transaction Date:', 'lhdn-e-invoice-receiver'); ?></strong> <?php echo isset($invoice_data['TransactionDate']) ? esc_html($invoice_data['TransactionDate']) : ''; ?></p>
                    <p><strong><?php _e('Transaction Time:', 'lhdn-e-invoice-receiver'); ?></strong> <?php echo isset($invoice_data['TransactionTime']) ? esc_html($invoice_data['TransactionTime']) : ''; ?></p>
                </div>
                
                <?php if (!empty($invoice->order_id)) : ?>
                    <div class="leir-order-link">
                        <a href="<?php echo esc_url(wc_get_account_endpoint_url('view-order') . $invoice->order_id); ?>" class="button">
                            <?php printf(__('View Order #%s', 'lhdn-e-invoice-receiver'), esc_html($invoice->order_id)); ?>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="leir-invoice-parties">
                <div class="leir-seller">
                    <h3><?php _e('Seller Information', 'lhdn-e-invoice-receiver'); ?></h3>
                    <p><strong><?php _e('Name:', 'lhdn-e-invoice-receiver'); ?></strong> <?php echo isset($invoice_data['SellerName']) ? esc_html($invoice_data['SellerName']) : ''; ?></p>
                    <p><strong><?php _e('TIN No:', 'lhdn-e-invoice-receiver'); ?></strong> <?php echo isset($invoice_data['SellerTinNo']) ? esc_html($invoice_data['SellerTinNo']) : ''; ?></p>
                    <p><strong><?php _e('Email:', 'lhdn-e-invoice-receiver'); ?></strong> <?php echo isset($invoice_data['SellerEmail']) ? esc_html($invoice_data['SellerEmail']) : ''; ?></p>
                    <p><strong><?php _e('Contact No:', 'lhdn-e-invoice-receiver'); ?></strong> <?php echo isset($invoice_data['SellerContactNo']) ? esc_html($invoice_data['SellerContactNo']) : ''; ?></p>
                    <p><strong><?php _e('Address:', 'lhdn-e-invoice-receiver'); ?></strong> <?php echo isset($invoice_data['SellerAddress']) ? esc_html($invoice_data['SellerAddress']) : ''; ?></p>
                </div>
                
                <div class="leir-buyer">
                    <h3><?php _e('Buyer Information', 'lhdn-e-invoice-receiver'); ?></h3>
                    <p><strong><?php _e('Name:', 'lhdn-e-invoice-receiver'); ?></strong> <?php echo isset($invoice_data['BuyerName']) ? esc_html($invoice_data['BuyerName']) : ''; ?></p>
                    <p><strong><?php _e('Email:', 'lhdn-e-invoice-receiver'); ?></strong> <?php echo isset($invoice_data['BuyerEmail']) ? esc_html($invoice_data['BuyerEmail']) : ''; ?></p>
                    <p><strong><?php _e('Contact No:', 'lhdn-e-invoice-receiver'); ?></strong> <?php echo isset($invoice_data['BuyerContactNo']) ? esc_html($invoice_data['BuyerContactNo']) : ''; ?></p>
                    <p><strong><?php _e('Address:', 'lhdn-e-invoice-receiver'); ?></strong> <?php echo isset($invoice_data['BuyerAddress']) ? esc_html($invoice_data['BuyerAddress']) : ''; ?></p>
                </div>
            </div>
            
            <div class="leir-invoice-items">
                <h3><?php _e('Items', 'lhdn-e-invoice-receiver'); ?></h3>
                <table class="woocommerce-table woocommerce-table--order-details shop_table order_details">
                    <thead>
                        <tr>
                            <th><?php _e('Item', 'lhdn-e-invoice-receiver'); ?></th>
                            <th><?php _e('Quantity', 'lhdn-e-invoice-receiver'); ?></th>
                            <th><?php _e('Unit Price', 'lhdn-e-invoice-receiver'); ?></th>
                            <th><?php _e('Amount', 'lhdn-e-invoice-receiver'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (isset($invoice_data['Items']) && is_array($invoice_data['Items'])) : ?>
                            <?php foreach ($invoice_data['Items'] as $item) : ?>
                                <tr>
                                    <td>
                                        <?php echo isset($item['Description']) ? esc_html($item['Description']) : '--'; ?>
                                    </td>
                                    <td>
                                        <?php echo isset($item['Quantity']) ? esc_html($item['Quantity']) : '0'; ?>
                                    </td>
                                    <td>
                                        <?php echo isset($item['UnitPrice']) ? wc_price($item['UnitPrice']) : wc_price(0); ?>
                                    </td>
                                    <td>
                                        <?php echo isset($item['SubtotalAmount']) ? wc_price($item['SubtotalAmount']) : wc_price(0); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                    <tfoot>
                        <tr>
                            <th scope="row" colspan="3"><?php _e('Subtotal:', 'lhdn-e-invoice-receiver'); ?></th>
                            <td><?php echo isset($invoice_data['TotalExcludingTax']) ? wc_price($invoice_data['TotalExcludingTax']) : ''; ?></td>
                        </tr>
                        <?php if (isset($invoice_data['TotalTaxAmount']) && $invoice_data['TotalTaxAmount'] > 0) : ?>
                            <tr>
                                <th scope="row" colspan="3"><?php _e('Tax:', 'lhdn-e-invoice-receiver'); ?></th>
                                <td><?php echo wc_price($invoice_data['TotalTaxAmount']); ?></td>
                            </tr>
                        <?php endif; ?>
                        <?php if (isset($invoice_data['TotalDiscount']) && $invoice_data['TotalDiscount'] > 0) : ?>
                            <tr>
                                <th scope="row" colspan="3"><?php _e('Discount:', 'lhdn-e-invoice-receiver'); ?></th>
                                <td>-<?php echo wc_price($invoice_data['TotalDiscount']); ?></td>
                            </tr>
                        <?php endif; ?>
                        <tr>
                            <th scope="row" colspan="3"><?php _e('Total:', 'lhdn-e-invoice-receiver'); ?></th>
                            <td><?php echo isset($invoice_data['TotalIncludingTax']) ? wc_price($invoice_data['TotalIncludingTax']) : ''; ?></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            
            <?php if (isset($invoice_data['Payments']) && is_array($invoice_data['Payments'])) : ?>
                <div class="leir-invoice-payments">
                    <h3><?php _e('Payment Information', 'lhdn-e-invoice-receiver'); ?></h3>
                    <table class="woocommerce-table">
                        <thead>
                            <tr>
                                <th><?php _e('Method', 'lhdn-e-invoice-receiver'); ?></th>
                                <th><?php _e('Amount', 'lhdn-e-invoice-receiver'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($invoice_data['Payments'] as $payment) : ?>
                                <tr>
                                    <td>
                                        <?php echo isset($payment['Method']) ? esc_html($payment['Method']) : '--'; ?>
                                    </td>
                                    <td>
                                        <?php echo isset($payment['Amount']) ? wc_price($payment['Amount']) : wc_price(0); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
            
            <div class="leir-invoice-footer">
                <?php if (isset($invoice_data['Notes'])) : ?>
                    <div class="leir-invoice-notes">
                        <h3><?php _e('Notes', 'lhdn-e-invoice-receiver'); ?></h3>
                        <p><?php echo esc_html($invoice_data['Notes']); ?></p>
                    </div>
                <?php endif; ?>
                
                <div class="leir-invoice-actions">
                    <button onclick="window.print();" class="button"><?php _e('Print Invoice', 'lhdn-e-invoice-receiver'); ?></button>
                </div>
            </div>
        </div>
        <?php
    }
} 