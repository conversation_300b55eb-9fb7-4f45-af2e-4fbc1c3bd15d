<?php
/**
 * Token Manager class
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class for managing API tokens
 */
class LEIR_Token_Manager {
    /**
     * Constructor
     */
    public function __construct() {
        // Add AJAX handlers
        add_action('wp_ajax_leir_generate_token', array($this, 'ajax_generate_token'));
        add_action('wp_ajax_leir_delete_token', array($this, 'ajax_delete_token'));
        add_action('wp_ajax_leir_check_token_setup', array($this, 'ajax_check_token_setup'));
        
        // Add user profile fields
        add_action('show_user_profile', array($this, 'add_token_fields'));
        add_action('edit_user_profile', array($this, 'add_token_fields'));
        
        // Enqueue scripts
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    /**
     * Enqueue scripts
     *
     * @param string $hook The current admin page
     */
    public function enqueue_scripts($hook) {
        if (!in_array($hook, array('profile.php', 'user-edit.php'))) {
            return;
        }
        
        wp_enqueue_script(
            'leir-token-manager-js',
            LEIR_PLUGIN_URL . 'admin/js/token-manager.js',
            array('jquery'),
            LEIR_VERSION,
            true
        );
        
        // Get admin-ajax.php URL - don't force HTTPS if site is HTTP
        $ajax_url = admin_url('admin-ajax.php');
        
        wp_localize_script(
            'leir-token-manager-js',
            'leirTokenManager',
            array(
                'ajaxUrl' => $ajax_url,
                'generateNonce' => wp_create_nonce('leir_generate_token'),
                'deleteNonce' => wp_create_nonce('leir_delete_token'),
                'confirmDelete' => __('Are you sure you want to delete this token?', 'lhdn-e-invoice-receiver'),
                'generating' => __('Generating token...', 'lhdn-e-invoice-receiver'),
                'error' => __('Error. Please try again.', 'lhdn-e-invoice-receiver')
            )
        );
    }
    
    /**
     * Add token fields to user profile
     *
     * @param WP_User $user The user object
     */
    public function add_token_fields($user) {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        $tokens = leir_get_user_tokens($user->ID);
        
        ?>
        <h2><?php _e('E-Invoice API Tokens', 'lhdn-e-invoice-receiver'); ?></h2>
        <table class="form-table">
            <tr>
                <th>
                    <label><?php _e('API Tokens', 'lhdn-e-invoice-receiver'); ?></label>
                </th>
                <td>
                    <div id="leir-tokens-container">
                        <?php if (empty($tokens)) : ?>
                            <p><?php _e('No tokens found.', 'lhdn-e-invoice-receiver'); ?></p>
                        <?php else : ?>
                            <table class="widefat fixed striped">
                                <thead>
                                    <tr>
                                        <th><?php _e('Website/Label', 'lhdn-e-invoice-receiver'); ?></th>
                                        <th><?php _e('Token', 'lhdn-e-invoice-receiver'); ?></th>
                                        <th><?php _e('Created', 'lhdn-e-invoice-receiver'); ?></th>
                                        <th><?php _e('Actions', 'lhdn-e-invoice-receiver'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($tokens as $token) : ?>
                                        <tr>
                                            <td>
                                                <?php echo esc_html($token->website_name ?: __('(No label)', 'lhdn-e-invoice-receiver')); ?>
                                            </td>
                                            <td>
                                                <code><?php echo esc_html($token->token); ?></code>
                                            </td>
                                            <td>
                                                <?php echo esc_html(leir_format_date($token->created)); ?>
                                            </td>
                                            <td>
                                                <a href="#" class="leir-delete-token" data-token="<?php echo esc_attr($token->token); ?>" data-user-id="<?php echo esc_attr($user->ID); ?>">
                                                    <?php _e('Delete', 'lhdn-e-invoice-receiver'); ?>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php endif; ?>
                    </div>
                    
                    <p>
                        <label for="leir-website-name"><?php _e('Website/Label (optional):', 'lhdn-e-invoice-receiver'); ?></label><br>
                        <input type="text" id="leir-website-name" name="leir-website-name" class="regular-text" placeholder="<?php esc_attr_e('e.g., My Online Store, Company Website', 'lhdn-e-invoice-receiver'); ?>" maxlength="255" />
                    </p>
                    <p>
                        <button type="button" class="button leir-generate-token" data-user-id="<?php echo esc_attr($user->ID); ?>">
                            <?php _e('Generate New Token', 'lhdn-e-invoice-receiver'); ?>
                        </button>
                        <button type="button" class="button button-secondary leir-check-setup" style="margin-left: 10px;">
                            <?php _e('Check Setup', 'lhdn-e-invoice-receiver'); ?>
                        </button>
                        <span class="leir-token-message"></span>
                    </p>
                    <p class="description"><?php _e('These tokens are used to authenticate API requests for e-invoice submissions.', 'lhdn-e-invoice-receiver'); ?></p>
                </td>
            </tr>
        </table>
        
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            console.log('Token manager initialized');
            console.log('User ID:', <?php echo json_encode($user->ID); ?>);
            console.log('Current tokens:', <?php echo json_encode($tokens); ?>);
            
            // Check setup button click handler
            $('.leir-check-setup').on('click', function(e) {
                e.preventDefault();
                
                var button = $(this);
                var messageSpan = $('.leir-token-message');
                
                $.ajax({
                    url: '<?php echo esc_js(admin_url('admin-ajax.php')); ?>',
                    type: 'POST',
                    data: {
                        action: 'leir_check_token_setup'
                    },
                    beforeSend: function() {
                        button.prop('disabled', true);
                        messageSpan.text('Checking setup...');
                    },
                    success: function(response) {
                        console.log('Setup check response:', response);
                        
                        if (response.success) {
                            var msg = 'Setup check completed. ';
                            if (response.data.table_exists) {
                                msg += 'Database table exists. ';
                                if (response.data.token_count !== undefined) {
                                    msg += 'Token count: ' + response.data.token_count + '. ';
                                }
                            } else {
                                msg += 'Database table MISSING! Please reactivate the plugin. ';
                            }
                            messageSpan.text(msg);
                        } else {
                            messageSpan.text('Setup check failed: ' + (response.data ? response.data.message : 'Unknown error'));
                        }
                        button.prop('disabled', false);
                        
                        setTimeout(function() {
                            messageSpan.text('');
                        }, 10000);
                    },
                    error: function(xhr, status, error) {
                        console.error('Setup check error:', status, error);
                        console.error('Response Text:', xhr.responseText);
                        messageSpan.text('Setup check failed. Check console for details.');
                        button.prop('disabled', false);
                    }
                });
            });
        });
        </script>
        <?php
    }
    
    /**
     * AJAX handler for generating token
     */
    public function ajax_generate_token() {
        // Clear any previous output
        if (ob_get_contents()) {
            ob_clean();
        }
        
        // Set proper headers
        header('Content-Type: application/json');
        
        // Check nonce
        if (!check_ajax_referer('leir_generate_token', 'nonce', false)) {
            wp_send_json_error(array(
                'message' => __('Security check failed.', 'lhdn-e-invoice-receiver')
            ));
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('Permission denied.', 'lhdn-e-invoice-receiver')
            ));
            return;
        }
        
        $user_id = isset($_POST['user_id']) ? absint($_POST['user_id']) : 0;
        $website_name = isset($_POST['website_name']) ? sanitize_text_field($_POST['website_name']) : '';

        if (!$user_id) {
            wp_send_json_error(array(
                'message' => __('Invalid user ID.', 'lhdn-e-invoice-receiver')
            ));
            return;
        }
        
        // Log for debugging
        error_log('Generating token for user ID: ' . $user_id);
        
        // Check if the database table exists
        global $wpdb;
        $table_name = $wpdb->prefix . 'leir_tokens';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        
        if (!$table_exists) {
            error_log("Table {$table_name} does not exist. Plugin may not be activated properly.");
            wp_send_json_error(array(
                'message' => __('Database tables not found. Please deactivate and reactivate the plugin.', 'lhdn-e-invoice-receiver')
            ));
            return;
        }
        
        // Check if a token was recently generated (within the last 5 seconds)
        // This helps prevent duplicate tokens if the button is clicked multiple times or JS execution is duplicated
        $recent_token = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT token FROM {$wpdb->prefix}leir_tokens 
                WHERE user_id = %d AND created >= DATE_SUB(NOW(), INTERVAL 5 SECOND) 
                ORDER BY created DESC LIMIT 1",
                $user_id
            )
        );
        
        if ($recent_token) {
            error_log('Recent token found, preventing duplicate generation');
            
            $tokens = leir_get_user_tokens($user_id);
            
            ob_start();
            $this->render_tokens_table($tokens, $user_id);
            $html = ob_get_clean();
            
            wp_send_json_success(array(
                'message' => __('Token already generated.', 'lhdn-e-invoice-receiver'),
                'html' => $html,
                'token_count' => count($tokens),
                'was_duplicate' => true
            ));
            return;
        }
        
        $token = leir_generate_user_token($user_id, $website_name);
        
        if ($token) {
            // Log success
            error_log('Token generated successfully: ' . substr($token, 0, 5) . '...');
            
            $tokens = leir_get_user_tokens($user_id);
            
            // Log token count
            error_log('Retrieved ' . count($tokens) . ' tokens for user');
            
            ob_start();
            $this->render_tokens_table($tokens, $user_id);
            $html = ob_get_clean();
            
            // Log HTML length
            error_log('Generated HTML length: ' . strlen($html));
            
            wp_send_json_success(array(
                'message' => __('Token generated successfully.', 'lhdn-e-invoice-receiver'),
                'html' => $html,
                'token_count' => count($tokens)
            ));
        } else {
            // Log failure
            error_log('Failed to generate token.');
            
            wp_send_json_error(array(
                'message' => __('Failed to generate token. Please check the error log for details.', 'lhdn-e-invoice-receiver')
            ));
        }
    }
    
    /**
     * Render the tokens table
     * 
     * @param array $tokens The tokens to render
     * @param int $user_id The user ID
     * @return void
     */
    private function render_tokens_table($tokens, $user_id) {
        ?>
        <table class="widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e('Website/Label', 'lhdn-e-invoice-receiver'); ?></th>
                    <th><?php _e('Token', 'lhdn-e-invoice-receiver'); ?></th>
                    <th><?php _e('Created', 'lhdn-e-invoice-receiver'); ?></th>
                    <th><?php _e('Actions', 'lhdn-e-invoice-receiver'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php
                if (!empty($tokens)) :
                    foreach ($tokens as $token_item) :
                ?>
                    <tr>
                        <td>
                            <?php echo esc_html($token_item->website_name ?: __('(No label)', 'lhdn-e-invoice-receiver')); ?>
                        </td>
                        <td>
                            <code><?php echo esc_html($token_item->token); ?></code>
                        </td>
                        <td>
                            <?php echo esc_html(leir_format_date($token_item->created)); ?>
                        </td>
                        <td>
                            <a href="#" class="leir-delete-token" data-token="<?php echo esc_attr($token_item->token); ?>" data-user-id="<?php echo esc_attr($user_id); ?>">
                                <?php _e('Delete', 'lhdn-e-invoice-receiver'); ?>
                            </a>
                        </td>
                    </tr>
                <?php 
                    endforeach;
                else:
                ?>
                    <tr>
                        <td colspan="4"><?php _e('No tokens found even after generation. This is unexpected.', 'lhdn-e-invoice-receiver'); ?></td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
        <?php
    }
    
    /**
     * AJAX handler for deleting token
     */
    public function ajax_delete_token() {
        // Clear any previous output
        if (ob_get_contents()) {
            ob_clean();
        }
        
        // Set proper headers
        header('Content-Type: application/json');
        
        // Check nonce
        if (!check_ajax_referer('leir_delete_token', 'nonce', false)) {
            wp_send_json_error(array(
                'message' => __('Security check failed.', 'lhdn-e-invoice-receiver')
            ));
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('Permission denied.', 'lhdn-e-invoice-receiver')
            ));
            return;
        }
        
        $token = isset($_POST['token']) ? sanitize_text_field($_POST['token']) : '';
        $user_id = isset($_POST['user_id']) ? absint($_POST['user_id']) : 0;
        
        if (!$token || !$user_id) {
            wp_send_json_error(array(
                'message' => __('Invalid token or user ID.', 'lhdn-e-invoice-receiver')
            ));
            return;
        }
        
        // Check if the database table exists
        global $wpdb;
        $table_name = $wpdb->prefix . 'leir_tokens';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        
        if (!$table_exists) {
            error_log("Table {$table_name} does not exist. Plugin may not be activated properly.");
            wp_send_json_error(array(
                'message' => __('Database tables not found. Please deactivate and reactivate the plugin.', 'lhdn-e-invoice-receiver')
            ));
            return;
        }
        
        $result = leir_delete_user_token($user_id, $token);
        
        if ($result) {
            $tokens = leir_get_user_tokens($user_id);
            
            ob_start();
            if (empty($tokens)) {
                echo '<p>' . __('No tokens found.', 'lhdn-e-invoice-receiver') . '</p>';
            } else {
                $this->render_tokens_table($tokens, $user_id);
            }
            $html = ob_get_clean();
            
            wp_send_json_success(array(
                'message' => __('Token deleted successfully.', 'lhdn-e-invoice-receiver'),
                'html' => $html
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to delete token.', 'lhdn-e-invoice-receiver')
            ));
        }
    }

    /**
     * AJAX handler for checking token setup
     */
    public function ajax_check_token_setup() {
        // Clear any previous output
        if (ob_get_contents()) {
            ob_clean();
        }
        
        // Set proper headers
        header('Content-Type: application/json');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('Permission denied.', 'lhdn-e-invoice-receiver')
            ));
            return;
        }
        
        global $wpdb;
        $diagnostics = array();
        
        // Check database tables
        $table_name = $wpdb->prefix . 'leir_tokens';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        $diagnostics['table_exists'] = $table_exists;
        
        if ($table_exists) {
            $token_count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
            $diagnostics['token_count'] = $token_count;
        }
        
        // Check if helper functions exist
        $diagnostics['helper_functions'] = array(
            'leir_generate_user_token' => function_exists('leir_generate_user_token'),
            'leir_get_user_tokens' => function_exists('leir_get_user_tokens'),
            'leir_delete_user_token' => function_exists('leir_delete_user_token'),
            'leir_format_date' => function_exists('leir_format_date')
        );
        
        // Check WordPress version
        $diagnostics['wp_version'] = get_bloginfo('version');
        $diagnostics['plugin_version'] = LEIR_VERSION;
        
        wp_send_json_success($diagnostics);
    }
} 