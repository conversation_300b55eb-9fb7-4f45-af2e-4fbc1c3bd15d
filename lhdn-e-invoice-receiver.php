<?php
/**
 * Plugin Name: LHDN E-Invoice Receiver
 * Plugin URI: https://yourwebsite.com/plugins/lhdn-e-invoice-receiver
 * Description: Receive e-invoice data from external sources with user-specific authorization tokens.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://yourwebsite.com
 * Text Domain: lhdn-e-invoice-receiver
 * Domain Path: /languages
 * Requires PHP: 7.4
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('LEIR_VERSION', '1.0.0');
define('LEIR_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('LEIR_PLUGIN_URL', plugin_dir_url(__FILE__));
define('LEIR_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Include required files
require_once LEIR_PLUGIN_DIR . 'includes/class-e-invoice-receiver.php';
require_once LEIR_PLUGIN_DIR . 'includes/helpers.php';
require_once LEIR_PLUGIN_DIR . 'includes/api.php';
require_once LEIR_PLUGIN_DIR . 'includes/class-subscription-handler.php';
require_once LEIR_PLUGIN_DIR . 'admin/class-admin.php';
require_once LEIR_PLUGIN_DIR . 'public/class-public.php';

// Initialize the plugin
function leir_init() {
    // Load text domain for translations
    load_plugin_textdomain('lhdn-e-invoice-receiver', false, dirname(LEIR_PLUGIN_BASENAME) . '/languages');
    
    // Initialize classes
    new LEIR_Admin();
    new LEIR_Public();
}
add_action('plugins_loaded', 'leir_init');

// Register activation hook
register_activation_hook(__FILE__, 'leir_activate');

// Activation function
function leir_activate() {
    // Create necessary database tables
    global $wpdb;
    
    $charset_collate = $wpdb->get_charset_collate();
    
    // Table for storing tokens
    $table_name = $wpdb->prefix . 'leir_tokens';

    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        token varchar(255) NOT NULL,
        website_name varchar(255) DEFAULT '' NOT NULL,
        created datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
        PRIMARY KEY  (id),
        UNIQUE KEY token (token),
        KEY user_id (user_id)
    ) $charset_collate;";
    
    // Table for storing received invoices
    $table_invoices = $wpdb->prefix . 'leir_invoices';
    
    $sql .= "CREATE TABLE $table_invoices (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        transaction_code varchar(100) NOT NULL,
        order_id bigint(20) DEFAULT NULL,
        invoice_data longtext NOT NULL,
        received_date datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
        PRIMARY KEY  (id),
        UNIQUE KEY transaction_code (transaction_code),
        KEY user_id (user_id)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);

    // Run database migration for existing installations
    leir_migrate_database();

    // Register the endpoints
    $public = new LEIR_Public();
    $public->add_einvoice_endpoint();

    // Register subscription endpoint
    $subscription_handler = new LEIR_Subscription_Handler();
    $subscription_handler->add_subscription_endpoint();

    // Flush rewrite rules
    flush_rewrite_rules();
}

/**
 * Migrate database for existing installations
 */
function leir_migrate_database() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'leir_tokens';

    // Check if website_name column exists
    $column_exists = $wpdb->get_results(
        $wpdb->prepare(
            "SHOW COLUMNS FROM {$table_name} LIKE %s",
            'website_name'
        )
    );

    // Add website_name column if it doesn't exist
    if (empty($column_exists)) {
        $wpdb->query(
            "ALTER TABLE {$table_name} ADD COLUMN website_name varchar(255) DEFAULT '' NOT NULL AFTER token"
        );

        // Log the migration
        error_log('LEIR: Added website_name column to tokens table');
    }
}

// Register deactivation hook
register_deactivation_hook(__FILE__, 'leir_deactivate');

// Deactivation function
function leir_deactivate() {
    // Clear scheduled events
    wp_clear_scheduled_hook('leir_check_expired_subscriptions');
    
    // Flush rewrite rules
    flush_rewrite_rules();
}

// Register uninstall hook
register_uninstall_hook(__FILE__, 'leir_uninstall');

// Uninstall function
function leir_uninstall() {
    // Option to remove plugin data
    if (get_option('leir_remove_data_on_uninstall', false)) {
        global $wpdb;
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}leir_tokens");
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}leir_invoices");
        
        // Remove plugin options
        delete_option('leir_settings');
        delete_option('leir_remove_data_on_uninstall');
        
        // Delete subscription data
        $subscription_posts = get_posts([
            'post_type' => 'leir_subscription',
            'numberposts' => -1,
            'fields' => 'ids'
        ]);
        
        foreach ($subscription_posts as $post_id) {
            wp_delete_post($post_id, true);
        }
        
        // Remove subscription product meta
        $subscription_products = get_posts([
            'post_type' => 'product',
            'meta_key' => '_leir_is_subscription',
            'meta_value' => '1',
            'numberposts' => -1,
            'fields' => 'ids'
        ]);
        
        foreach ($subscription_products as $product_id) {
            delete_post_meta($product_id, '_leir_is_subscription');
            delete_post_meta($product_id, '_leir_subscription_duration');
        }
    }
}

// Enqueue styles for public-facing pages
function leir_enqueue_public_styles() {
    // Check if it's the WooCommerce My Account page or our custom My Account page template
    if (is_account_page() || is_page_template('page-templates/my-account.php')) {
        wp_enqueue_style(
            'leir-public-styles',
            LEIR_PLUGIN_URL . 'assets/css/lhdn-e-invoice-public.css',
            array(),
            LEIR_VERSION
        );
        
        // Enqueue My Account horizontal tabs styles
        wp_enqueue_style(
            'leir-my-account-tabs',
            LEIR_PLUGIN_URL . 'assets/css/lhdn-e-invoice-my-account.css',
            array(),
            LEIR_VERSION . '.2'
        );
        
        // Enqueue subscription styles
        wp_enqueue_style(
            'leir-subscription-styles',
            LEIR_PLUGIN_URL . 'assets/css/lhdn-e-invoice-subscriptions.css',
            array(),
            LEIR_VERSION
        );
    }
}
add_action('wp_enqueue_scripts', 'leir_enqueue_public_styles'); 

/**
 * Register custom page templates
 * 
 * @param array $templates Existing templates
 * @return array Modified templates
 */
function leir_add_page_templates($templates) {
    $templates['page-templates/my-account.php'] = __('My Account', 'lhdn-e-invoice-receiver');
    return $templates;
}
add_filter('theme_page_templates', 'leir_add_page_templates');

/**
 * Load custom page template
 * 
 * @param string $template The path of the template to include
 * @return string The path of the template to include
 */
function leir_load_page_template($template) {
    global $post;
    
    if (!$post) {
        return $template;
    }
    
    // Get template slug
    $template_slug = get_page_template_slug($post->ID);
    
    // Check if it's our template
    if ($template_slug === 'page-templates/my-account.php') {
        $template = LEIR_PLUGIN_DIR . 'templates/page-templates/my-account.php';
    }
    
    return $template;
}
add_filter('template_include', 'leir_load_page_template', 99); 