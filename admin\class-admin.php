<?php
/**
 * Admin functionality for E-Invoice Receiver
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin class for E-Invoice Receiver
 */
class LEIR_Admin {
    /**
     * Constructor
     */
    public function __construct() {
        // Add menu items
        add_action('admin_menu', array($this, 'add_menu_items'));
        
        // Register settings
        add_action('admin_init', array($this, 'register_settings'));
        
        // Initialize token manager
        require_once LEIR_PLUGIN_DIR . 'admin/class-token-manager.php';
        new LEIR_Token_Manager();
        
        // Enqueue admin styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_styles'));
        
        // Handle user subscription assignment
        add_action('admin_post_leir_assign_subscription', array($this, 'handle_assign_subscription'));
    }
    
    /**
     * Add menu items
     */
    public function add_menu_items() {
        // Add main menu item
        add_menu_page(
            __('E-Invoice Receiver', 'lhdn-e-invoice-receiver'),
            __('E-Invoice', 'lhdn-e-invoice-receiver'),
            'manage_options',
            'lhdn-e-invoice-receiver',
            array($this, 'display_main_page'),
            'dashicons-media-text',
            30
        );
        
        // Add submenu items
        add_submenu_page(
            'lhdn-e-invoice-receiver',
            __('Dashboard', 'lhdn-e-invoice-receiver'),
            __('Dashboard', 'lhdn-e-invoice-receiver'),
            'manage_options',
            'lhdn-e-invoice-receiver',
            array($this, 'display_main_page')
        );
        
        add_submenu_page(
            'lhdn-e-invoice-receiver',
            __('Settings', 'lhdn-e-invoice-receiver'),
            __('Settings', 'lhdn-e-invoice-receiver'),
            'manage_options',
            'lhdn-e-invoice-settings',
            array($this, 'display_settings_page')
        );
        
        add_submenu_page(
            'lhdn-e-invoice-receiver',
            __('Invoices', 'lhdn-e-invoice-receiver'),
            __('Invoices', 'lhdn-e-invoice-receiver'),
            'manage_options',
            'lhdn-e-invoice-invoices',
            array($this, 'display_invoices_page')
        );
        
        add_submenu_page(
            'lhdn-e-invoice-receiver',
            __('User Subscriptions', 'lhdn-e-invoice-receiver'),
            __('User Subscriptions', 'lhdn-e-invoice-receiver'),
            'manage_options',
            'lhdn-e-invoice-user-subscriptions',
            array($this, 'display_user_subscriptions_page')
        );
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        // Register settings group
        register_setting(
            'leir_settings',
            'leir_settings',
            array($this, 'sanitize_settings')
        );
        
        // Add settings sections
        add_settings_section(
            'leir_general_section',
            __('General Settings', 'lhdn-e-invoice-receiver'),
            function() {
                echo '<p>' . __('Configure general settings for the E-Invoice Receiver plugin.', 'lhdn-e-invoice-receiver') . '</p>';
            },
            'leir_settings'
        );
        
        // Add Seller Information section
        add_settings_section(
            'leir_seller_section',
            __('Seller Information', 'lhdn-e-invoice-receiver'),
            function() {
                echo '<p>' . __('Configure seller information for the e-invoices.', 'lhdn-e-invoice-receiver') . '</p>';
            },
            'leir_settings'
        );
        
        // Add settings fields
        add_settings_field(
            'leir_debug_mode',
            __('Debug Mode', 'lhdn-e-invoice-receiver'),
            array($this, 'debug_mode_callback'),
            'leir_settings',
            'leir_general_section'
        );
        
        add_settings_field(
            'leir_remove_data_on_uninstall',
            __('Remove Data on Uninstall', 'lhdn-e-invoice-receiver'),
            array($this, 'remove_data_callback'),
            'leir_settings',
            'leir_general_section'
        );
        
        // Add Seller Information fields
        add_settings_field(
            'leir_seller_name',
            __('Company Name', 'lhdn-e-invoice-receiver'),
            array($this, 'seller_name_callback'),
            'leir_settings',
            'leir_seller_section'
        );
        
        add_settings_field(
            'leir_seller_tin_no',
            __('TIN No', 'lhdn-e-invoice-receiver'),
            array($this, 'seller_tin_no_callback'),
            'leir_settings',
            'leir_seller_section'
        );
        
        add_settings_field(
            'leir_seller_id_type',
            __('ID Type', 'lhdn-e-invoice-receiver'),
            array($this, 'seller_id_type_callback'),
            'leir_settings',
            'leir_seller_section'
        );
        
        add_settings_field(
            'leir_seller_id_value',
            __('ID Value', 'lhdn-e-invoice-receiver'),
            array($this, 'seller_id_value_callback'),
            'leir_settings',
            'leir_seller_section'
        );
        
        add_settings_field(
            'leir_seller_contact_no',
            __('Contact No', 'lhdn-e-invoice-receiver'),
            array($this, 'seller_contact_no_callback'),
            'leir_settings',
            'leir_seller_section'
        );
        
        add_settings_field(
            'leir_seller_address',
            __('Address', 'lhdn-e-invoice-receiver'),
            array($this, 'seller_address_callback'),
            'leir_settings',
            'leir_seller_section'
        );
        
        add_settings_field(
            'leir_seller_msic_code',
            __('MSIC Code', 'lhdn-e-invoice-receiver'),
            array($this, 'seller_msic_code_callback'),
            'leir_settings',
            'leir_seller_section'
        );
        
        add_settings_field(
            'leir_seller_city_name',
            __('City Name', 'lhdn-e-invoice-receiver'),
            array($this, 'seller_city_name_callback'),
            'leir_settings',
            'leir_seller_section'
        );
        
        add_settings_field(
            'leir_seller_state_code',
            __('State Code', 'lhdn-e-invoice-receiver'),
            array($this, 'seller_state_code_callback'),
            'leir_settings',
            'leir_seller_section'
        );
        
        add_settings_field(
            'leir_seller_sst_no',
            __('SST No', 'lhdn-e-invoice-receiver'),
            array($this, 'seller_sst_no_callback'),
            'leir_settings',
            'leir_seller_section'
        );
        
        add_settings_field(
            'leir_seller_email',
            __('Email', 'lhdn-e-invoice-receiver'),
            array($this, 'seller_email_callback'),
            'leir_settings',
            'leir_seller_section'
        );
        
        add_settings_field(
            'leir_seller_branch_name',
            __('Branch Name', 'lhdn-e-invoice-receiver'),
            array($this, 'seller_branch_name_callback'),
            'leir_settings',
            'leir_seller_section'
        );
    }
    
    /**
     * Display the main admin page
     */
    public function display_main_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <div class="card">
                <h2><?php _e('E-Invoice Receiver', 'lhdn-e-invoice-receiver'); ?></h2>
                <p><?php _e('This plugin allows you to receive e-invoice data from external sources.', 'lhdn-e-invoice-receiver'); ?></p>
                <p><?php _e('Each user has their own unique authentication token to secure their e-invoice submissions.', 'lhdn-e-invoice-receiver'); ?></p>
                
                <?php echo leir_get_api_help_text(); ?>
                
                <p><a href="<?php echo esc_url(admin_url('admin.php?page=lhdn-e-invoice-settings')); ?>" class="button button-primary"><?php _e('Settings', 'lhdn-e-invoice-receiver'); ?></a></p>
            </div>
            
            <?php $this->display_recent_invoices(); ?>
        </div>
        <?php
    }
    
    /**
     * Display the settings page
     */
    public function display_settings_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <form method="post" action="options.php">
                <?php
                settings_fields('leir_settings');
                do_settings_sections('leir_settings');
                submit_button();
                ?>
            </form>
        </div>
        <?php
    }
    
    /**
     * Display the invoices page
     */
    public function display_invoices_page() {
        $user_id = get_current_user_id();
        $invoices = leir_get_user_invoices($user_id);
        
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <?php if (empty($invoices)) : ?>
                <div class="notice notice-info">
                    <p><?php _e('No invoices found.', 'lhdn-e-invoice-receiver'); ?></p>
                </div>
            <?php else : ?>
                <style>
                    .invoice-details ul {
                        margin: 0;
                        padding-left: 20px;
                    }
                    .invoice-data-section {
                        margin-bottom: 10px;
                        border-bottom: 1px solid #eee;
                        padding-bottom: 10px;
                    }
                    .invoice-data-section:last-child {
                        border-bottom: none;
                    }
                    .invoice-data-heading {
                        font-weight: bold;
                        margin-bottom: 5px;
                    }
                    .invoice-modal {
                        display: none;
                        position: fixed;
                        z-index: 999;
                        left: 0;
                        top: 0;
                        width: 100%;
                        height: 100%;
                        overflow: auto;
                        background-color: rgba(0,0,0,0.4);
                    }
                    .invoice-modal-content {
                        background-color: #fefefe;
                        margin: 5% auto;
                        padding: 20px;
                        border: 1px solid #ddd;
                        width: 80%;
                        max-width: 900px;
                        border-radius: 3px;
                    }
                    .invoice-modal-close {
                        color: #aaa;
                        float: right;
                        font-size: 28px;
                        font-weight: bold;
                        cursor: pointer;
                    }
                    .invoice-modal-close:hover {
                        color: black;
                    }
                    .invoice-modal-grid {
                        display: grid;
                        grid-template-columns: repeat(3, 1fr);
                        gap: 20px;
                    }
                    .invoice-modal-section {
                        margin-bottom: 20px;
                    }
                    .invoice-modal-heading {
                        font-size: 16px;
                        padding-bottom: 5px;
                        margin-bottom: 10px;
                        border-bottom: 1px solid #eee;
                    }
                </style>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Transaction Code', 'lhdn-e-invoice-receiver'); ?></th>
                            <th><?php _e('Order ID', 'lhdn-e-invoice-receiver'); ?></th>
                            <th><?php _e('Buyer Name', 'lhdn-e-invoice-receiver'); ?></th>
                            <th><?php _e('Date', 'lhdn-e-invoice-receiver'); ?></th>
                            <th><?php _e('Total', 'lhdn-e-invoice-receiver'); ?></th>
                            <th><?php _e('Actions', 'lhdn-e-invoice-receiver'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($invoices as $invoice) : 
                            $invoice_data = leir_get_invoice($invoice->id);
                            if (!$invoice_data) continue;
                            $data = $invoice_data->invoice_data;
                        ?>
                            <tr>
                                <td><?php echo esc_html($invoice->transaction_code); ?></td>
                                <td><?php echo esc_html($invoice->order_id); ?></td>
                                <td><?php echo esc_html($data['BuyerName'] ?? ''); ?></td>
                                <td><?php echo esc_html(leir_format_date($invoice->received_date)); ?></td>
                                <td><?php echo esc_html($data['TotalIncludingTax'] ?? ''); ?></td>
                                <td>
                                    <button class="button button-small view-invoice-details" data-invoice-id="<?php echo esc_attr($invoice->id); ?>">
                                        <?php _e('View Details', 'lhdn-e-invoice-receiver'); ?>
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>

                <!-- Invoice Details Modal -->
                <div id="invoice-details-modal" class="invoice-modal">
                    <div class="invoice-modal-content">
                        <span class="invoice-modal-close">&times;</span>
                        <h2><?php _e('Invoice Details', 'lhdn-e-invoice-receiver'); ?></h2>
                        <div id="invoice-details-content" class="invoice-modal-grid">
                            <!-- Content loaded dynamically via JS -->
                        </div>
                    </div>
                </div>

                <script>
                jQuery(document).ready(function($) {
                    // Store invoice data in JavaScript object for modal
                    var invoiceData = {};
                    
                    <?php foreach ($invoices as $invoice) : 
                        $invoice_data = leir_get_invoice($invoice->id);
                        if (!$invoice_data) continue;
                    ?>
                    invoiceData[<?php echo esc_js($invoice->id); ?>] = <?php echo wp_json_encode($invoice_data); ?>;
                    <?php endforeach; ?>
                    
                    // Show modal when View Details button is clicked
                    $('.view-invoice-details').on('click', function() {
                        var invoiceId = $(this).data('invoice-id');
                        var invoice = invoiceData[invoiceId];
                        var data = invoice.invoice_data;
                        
                        // Build the modal content HTML
                        var html = '<div class="invoice-modal-section">';
                        html += '<h3 class="invoice-modal-heading"><?php _e('Transaction Info', 'lhdn-e-invoice-receiver'); ?></h3>';
                        html += '<div><strong><?php _e('Transaction Code', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + invoice.transaction_code + '</div>';
                        html += '<div><strong><?php _e('Order ID', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + invoice.order_id + '</div>';
                        html += '<div><strong><?php _e('Transaction Date', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + (data.TransactionDate || '') + '</div>';
                        html += '<div><strong><?php _e('Received Date', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + formatDate(invoice.received_date) + '</div>';
                        
                        <?php if (current_user_can('manage_options')) : ?>
                        html += '<div><strong><?php _e('User ID', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + invoice.user_id + '</div>';
                        <?php endif; ?>
                        html += '</div>';
                        
                        // Add Seller Information section
                        html += '<div class="invoice-modal-section">';
                        html += '<h3 class="invoice-modal-heading"><?php _e('Seller Details', 'lhdn-e-invoice-receiver'); ?></h3>';
                        html += '<div><strong><?php _e('Seller Name', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + (data.SellerName || '') + '</div>';
                        if (data.SellerTinNo) {
                            html += '<div><strong><?php _e('Seller TIN No', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + data.SellerTinNo + '</div>';
                        }
                        if (data.SellerIdType) {
                            html += '<div><strong><?php _e('Seller ID Type', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + data.SellerIdType + '</div>';
                        }
                        if (data.SellerIdValue) {
                            html += '<div><strong><?php _e('Seller ID Value', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + data.SellerIdValue + '</div>';
                        }
                        if (data.SellerSstNo) {
                            html += '<div><strong><?php _e('Seller SST No', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + data.SellerSstNo + '</div>';
                        }
                        if (data.SellerEmail) {
                            html += '<div><strong><?php _e('Seller Email', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + data.SellerEmail + '</div>';
                        }
                        if (data.SellerAddress) {
                            html += '<div><strong><?php _e('Seller Address', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + data.SellerAddress + '</div>';
                        }
                        html += '</div>';
                        
                        html += '<div class="invoice-modal-section">';
                        html += '<h3 class="invoice-modal-heading"><?php _e('Buyer Details', 'lhdn-e-invoice-receiver'); ?></h3>';
                        html += '<div><strong><?php _e('Buyer Name', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + (data.BuyerName || '') + '</div>';
                        if (data.BuyerEmail) {
                            html += '<div><strong><?php _e('Buyer Email', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + data.BuyerEmail + '</div>';
                        }
                        if (data.BuyerPhone) {
                            html += '<div><strong><?php _e('Buyer Phone', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + data.BuyerPhone + '</div>';
                        }
                        if (data.BuyerAddress) {
                            html += '<div><strong><?php _e('Buyer Address', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + data.BuyerAddress + '</div>';
                        }
                        if (data.BuyerTaxID) {
                            html += '<div><strong><?php _e('Buyer Tax ID', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + data.BuyerTaxID + '</div>';
                        }
                        html += '</div>';
                        
                        html += '<div class="invoice-modal-section">';
                        html += '<h3 class="invoice-modal-heading"><?php _e('Payment Info', 'lhdn-e-invoice-receiver'); ?></h3>';
                        if (data.TotalIncludingTax) {
                            html += '<div><strong><?php _e('Total', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + data.TotalIncludingTax + '</div>';
                        }
                        if (data.TotalTaxAmount) {
                            html += '<div><strong><?php _e('Tax Amount', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + data.TotalTaxAmount + '</div>';
                        }
                        if (data.TaxCode) {
                            html += '<div><strong><?php _e('Tax Code', 'lhdn-e-invoice-receiver'); ?>:</strong> ' + data.TaxCode + '</div>';
                        }
                        html += '</div>';
                        
                        if (data.Items && data.Items.length > 0) {
                            html += '<div class="invoice-modal-section" style="grid-column: span 3;">';
                            html += '<h3 class="invoice-modal-heading"><?php _e('Items', 'lhdn-e-invoice-receiver'); ?></h3>';
                            html += '<table class="widefat" style="width:100%"><thead><tr>';
                            html += '<th><?php _e('Description', 'lhdn-e-invoice-receiver'); ?></th>';
                            html += '<th><?php _e('Quantity', 'lhdn-e-invoice-receiver'); ?></th>';
                            html += '<th><?php _e('Unit Price', 'lhdn-e-invoice-receiver'); ?></th>';
                            html += '<th><?php _e('Subtotal', 'lhdn-e-invoice-receiver'); ?></th>';
                            html += '<th><?php _e('Tax', 'lhdn-e-invoice-receiver'); ?></th>';
                            html += '</tr></thead><tbody>';
                            
                            for (var i = 0; i < data.Items.length; i++) {
                                var item = data.Items[i];
                                html += '<tr>';
                                html += '<td>' + (item.Description || '') + '</td>';
                                html += '<td>' + (item.Quantity || '') + '</td>';
                                html += '<td>' + (item.UnitPrice || '') + '</td>';
                                html += '<td>' + (item.SubtotalAmount || '') + '</td>';
                                html += '<td>' + (item.TaxAmount || '') + '</td>';
                                html += '</tr>';
                            }
                            
                            html += '</tbody></table>';
                            html += '</div>';
                        }
                        
                        // Additional fields section - for anything that's in the data but not specifically handled above
                        var displayedFields = [
                            'TransactionCode', 'OrderID', 'TransactionDate', 'TransactionTime',
                            'BuyerName', 'BuyerTaxID', 'BuyerEmail', 'BuyerPhone', 'BuyerAddress',
                            'Items', 'Payments', 'TotalIncludingTax', 'TaxCode', 'TotalTaxAmount', 
                            'TaxBreakdown', 'Notes', 'CustomFields', 'SenderWebsiteURL',
                            'SellerName', 'SellerTinNo', 'SellerIdType', 'SellerIdValue', 'SellerSstNo',
                            'SellerEmail', 'SellerAddress', 'SellerCityName', 'SellerStateCode',
                            'SellerContactNo', 'SellerBranchName', 'SellerMsicCode'
                        ];
                        
                        var additionalFields = [];
                        for (var field in data) {
                            if (displayedFields.indexOf(field) === -1 && typeof data[field] !== 'object') {
                                additionalFields.push(field);
                            }
                        }
                        
                        if (additionalFields.length > 0) {
                            html += '<div class="invoice-modal-section" style="grid-column: span 3;">';
                            html += '<h3 class="invoice-modal-heading"><?php _e('Other Information', 'lhdn-e-invoice-receiver'); ?></h3>';
                            html += '<div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">';
                            
                            for (var i = 0; i < additionalFields.length; i++) {
                                var field = additionalFields[i];
                                html += '<div><strong>' + field + ':</strong> ' + data[field] + '</div>';
                            }
                            
                            html += '</div></div>';
                        }
                        
                        // Set the content and display the modal
                        $('#invoice-details-content').html(html);
                        $('#invoice-details-modal').show();
                    });
                    
                    // Close modal when X is clicked
                    $('.invoice-modal-close').on('click', function() {
                        $('#invoice-details-modal').hide();
                    });
                    
                    // Close modal when clicking outside the content
                    $(window).on('click', function(event) {
                        if ($(event.target).is('#invoice-details-modal')) {
                            $('#invoice-details-modal').hide();
                        }
                    });
                    
                    // Helper function to format dates
                    function formatDate(dateString) {
                        var date = new Date(dateString);
                        return date.toLocaleString();
                    }
                });
                </script>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * Display user subscriptions page
     */
    public function display_user_subscriptions_page() {
        // Handle messages
        $message = '';
        $message_type = '';
        if (isset($_GET['message'])) {
            switch ($_GET['message']) {
                case 'assigned':
                    $message = __('Subscription assigned successfully!', 'lhdn-e-invoice-receiver');
                    $message_type = 'success';
                    break;
                case 'error':
                    $message = __('Error assigning subscription. Please try again.', 'lhdn-e-invoice-receiver');
                    $message_type = 'error';
                    break;
                case 'invalid_user':
                    $message = __('Invalid user selected.', 'lhdn-e-invoice-receiver');
                    $message_type = 'error';
                    break;
            }
        }
        ?>
        <div class="wrap">
            <h1><?php _e('User Subscriptions', 'lhdn-e-invoice-receiver'); ?></h1>
            
            <?php if ($message): ?>
                <div class="notice notice-<?php echo esc_attr($message_type); ?> is-dismissible">
                    <p><?php echo esc_html($message); ?></p>
                </div>
            <?php endif; ?>
            
            <div class="leir-admin-content">
                <div class="leir-admin-section">
                    <h2><?php _e('Assign New Subscription', 'lhdn-e-invoice-receiver'); ?></h2>
                    
                    <form method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>" class="leir-assign-subscription-form">
                        <?php wp_nonce_field('leir_assign_subscription', 'leir_assign_subscription_nonce'); ?>
                        <input type="hidden" name="action" value="leir_assign_subscription">
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="user_id"><?php _e('Select User', 'lhdn-e-invoice-receiver'); ?></label>
                                </th>
                                <td>
                                    <select name="user_id" id="user_id" class="regular-text" required>
                                        <option value=""><?php _e('Select a user...', 'lhdn-e-invoice-receiver'); ?></option>
                                        <?php
                                        $users = get_users();
                                        foreach ($users as $user) {
                                            printf(
                                                '<option value="%d">%s (%s)</option>',
                                                $user->ID,
                                                esc_html($user->display_name),
                                                esc_html($user->user_email)
                                            );
                                        }
                                        ?>
                                    </select>
                                    <p class="description"><?php _e('Select the user to assign the subscription to.', 'lhdn-e-invoice-receiver'); ?></p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="duration"><?php _e('Duration (months)', 'lhdn-e-invoice-receiver'); ?></label>
                                </th>
                                <td>
                                    <input type="number" name="duration" id="duration" value="12" min="1" max="60" class="small-text" required>
                                    <p class="description"><?php _e('Number of months for the subscription (1-60 months).', 'lhdn-e-invoice-receiver'); ?></p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="start_date"><?php _e('Start Date', 'lhdn-e-invoice-receiver'); ?></label>
                                </th>
                                <td>
                                    <input type="date" name="start_date" id="start_date" value="<?php echo esc_attr(date('Y-m-d')); ?>" class="regular-text" required>
                                    <p class="description"><?php _e('When the subscription should start.', 'lhdn-e-invoice-receiver'); ?></p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="status"><?php _e('Status', 'lhdn-e-invoice-receiver'); ?></label>
                                </th>
                                <td>
                                    <select name="status" id="status" class="regular-text" required>
                                        <option value="active"><?php _e('Active', 'lhdn-e-invoice-receiver'); ?></option>
                                        <option value="pending"><?php _e('Pending', 'lhdn-e-invoice-receiver'); ?></option>
                                    </select>
                                    <p class="description"><?php _e('Initial status of the subscription.', 'lhdn-e-invoice-receiver'); ?></p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="notes"><?php _e('Notes', 'lhdn-e-invoice-receiver'); ?></label>
                                </th>
                                <td>
                                    <textarea name="notes" id="notes" rows="3" class="large-text" placeholder="<?php _e('Optional notes about this subscription assignment...', 'lhdn-e-invoice-receiver'); ?>"></textarea>
                                </td>
                            </tr>
                        </table>
                        
                        <?php submit_button(__('Assign Subscription', 'lhdn-e-invoice-receiver'), 'primary', 'submit', false); ?>
                    </form>
                </div>
                
                <div class="leir-admin-section">
                    <h2><?php _e('Current Subscriptions', 'lhdn-e-invoice-receiver'); ?></h2>
                    <?php $this->display_subscriptions_table(); ?>
                </div>
            </div>
        </div>
        
        <style>
        .leir-admin-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-top: 1rem;
        }
        
        .leir-admin-section {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 1.5rem;
        }
        
        .leir-admin-section h2 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 0.5rem;
        }
        
        .leir-assign-subscription-form .form-table th {
            width: 200px;
        }
        
        @media (max-width: 1200px) {
            .leir-admin-content {
                grid-template-columns: 1fr;
            }
        }
        </style>
        <?php
    }
    
    /**
     * Display subscriptions table
     */
    private function display_subscriptions_table() {
        $args = array(
            'post_type' => 'leir_subscription',
            'posts_per_page' => 20,
            'post_status' => 'publish',
            'orderby' => 'date',
            'order' => 'DESC'
        );
        
        $subscriptions = get_posts($args);
        
        if (empty($subscriptions)) {
            echo '<p>' . __('No subscriptions found.', 'lhdn-e-invoice-receiver') . '</p>';
            return;
        }
        ?>
        
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e('User', 'lhdn-e-invoice-receiver'); ?></th>
                    <th><?php _e('Start Date', 'lhdn-e-invoice-receiver'); ?></th>
                    <th><?php _e('Expiry Date', 'lhdn-e-invoice-receiver'); ?></th>
                    <th><?php _e('Status', 'lhdn-e-invoice-receiver'); ?></th>
                    <th><?php _e('Source', 'lhdn-e-invoice-receiver'); ?></th>
                    <th><?php _e('Actions', 'lhdn-e-invoice-receiver'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($subscriptions as $subscription): 
                    $user_id = get_post_meta($subscription->ID, '_leir_user_id', true);
                    $user = get_userdata($user_id);
                    $start_date = get_post_meta($subscription->ID, '_leir_start_date', true);
                    $expiry_date = get_post_meta($subscription->ID, '_leir_expiry_date', true);
                    $status = get_post_meta($subscription->ID, '_leir_status', true);
                    $order_id = get_post_meta($subscription->ID, '_leir_order_id', true);
                    $notes = get_post_meta($subscription->ID, '_leir_admin_notes', true);
                    
                    $status_class = 'subscription-status-' . esc_attr($status);
                ?>
                <tr>
                    <td>
                        <?php if ($user): ?>
                            <strong><?php echo esc_html($user->display_name); ?></strong><br>
                            <small><?php echo esc_html($user->user_email); ?></small>
                        <?php else: ?>
                            <em><?php _e('User not found', 'lhdn-e-invoice-receiver'); ?></em>
                        <?php endif; ?>
                    </td>
                    <td><?php echo $start_date ? esc_html(date_i18n(get_option('date_format'), strtotime($start_date))) : '-'; ?></td>
                    <td>
                        <?php if ($expiry_date): ?>
                            <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($expiry_date))); ?>
                            <?php if (strtotime($expiry_date) < time() && $status === 'active'): ?>
                                <span style="color: #d63384;"> (<?php _e('Expired', 'lhdn-e-invoice-receiver'); ?>)</span>
                            <?php endif; ?>
                        <?php else: ?>
                            -
                        <?php endif; ?>
                    </td>
                    <td>
                        <span class="<?php echo esc_attr($status_class); ?>">
                            <?php echo esc_html(ucfirst($status)); ?>
                        </span>
                    </td>
                    <td>
                        <?php if ($order_id): ?>
                            <a href="<?php echo esc_url(admin_url('post.php?post=' . $order_id . '&action=edit')); ?>">
                                <?php printf(__('Order #%s', 'lhdn-e-invoice-receiver'), $order_id); ?>
                            </a>
                        <?php else: ?>
                            <?php _e('Admin Assignment', 'lhdn-e-invoice-receiver'); ?>
                            <?php if ($notes): ?>
                                <br><small><?php echo esc_html($notes); ?></small>
                            <?php endif; ?>
                        <?php endif; ?>
                    </td>
                    <td>
                        <a href="<?php echo esc_url(admin_url('post.php?post=' . $subscription->ID . '&action=edit')); ?>" class="button button-small">
                            <?php _e('Edit', 'lhdn-e-invoice-receiver'); ?>
                        </a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <style>
        .subscription-status-active { color: #00a32a; font-weight: bold; }
        .subscription-status-expired { color: #d63384; font-weight: bold; }
        .subscription-status-pending { color: #dba617; font-weight: bold; }
        </style>
        <?php
    }
    
    /**
     * Display recent invoices on the main page
     */
    private function display_recent_invoices() {
        $user_id = get_current_user_id();
        $invoices = leir_get_user_invoices($user_id, 5, 0);
        
        if (empty($invoices)) {
            return;
        }
        
        ?>
        <div class="card">
            <h2><?php _e('Recent Invoices', 'lhdn-e-invoice-receiver'); ?></h2>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Transaction Code', 'lhdn-e-invoice-receiver'); ?></th>
                        <th><?php _e('Order ID', 'lhdn-e-invoice-receiver'); ?></th>
                        <?php if (current_user_can('manage_options')) : ?>
                        <th><?php _e('Username', 'lhdn-e-invoice-receiver'); ?></th>
                        <th><?php _e('User Email', 'lhdn-e-invoice-receiver'); ?></th>
                        <?php endif; ?>
                        <th><?php _e('Received Date', 'lhdn-e-invoice-receiver'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($invoices as $invoice) : ?>
                        <tr>
                            <td><?php echo esc_html($invoice->transaction_code); ?></td>
                            <td><?php echo esc_html($invoice->order_id); ?></td>
                            <?php if (current_user_can('manage_options')) : 
                                $user = get_user_by('id', $invoice->user_id);
                            ?>
                            <td><?php echo $user ? esc_html($user->user_login) : 'N/A'; ?></td>
                            <td><?php echo $user ? esc_html($user->user_email) : 'N/A'; ?></td>
                            <?php endif; ?>
                            <td><?php echo esc_html(leir_format_date($invoice->received_date)); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <p><a href="<?php echo esc_url(admin_url('admin.php?page=lhdn-e-invoice-invoices')); ?>" class="button"><?php _e('View All Invoices', 'lhdn-e-invoice-receiver'); ?></a></p>
        </div>
        <?php
    }
    
    /**
     * Debug mode setting callback
     */
    public function debug_mode_callback() {
        $settings = get_option('leir_settings', array());
        $debug_mode = isset($settings['debug_mode']) ? $settings['debug_mode'] : false;
        
        ?>
        <label for="leir_settings[debug_mode]">
            <input type="checkbox" id="leir_settings[debug_mode]" name="leir_settings[debug_mode]" value="1" <?php checked($debug_mode, true); ?>>
            <?php _e('Enable debug mode', 'lhdn-e-invoice-receiver'); ?>
        </label>
        <p class="description"><?php _e('This will log API requests and responses to the debug.log file in the plugin directory.', 'lhdn-e-invoice-receiver'); ?></p>
        <?php
    }
    
    /**
     * Remove data setting callback
     */
    public function remove_data_callback() {
        $remove_data = get_option('leir_remove_data_on_uninstall', false);
        
        ?>
        <label for="leir_remove_data_on_uninstall">
            <input type="checkbox" id="leir_remove_data_on_uninstall" name="leir_remove_data_on_uninstall" value="1" <?php checked($remove_data, true); ?>>
            <?php _e('Remove all plugin data when uninstalling', 'lhdn-e-invoice-receiver'); ?>
        </label>
        <p class="description"><?php _e('This will remove all tokens, invoices, and settings when the plugin is uninstalled.', 'lhdn-e-invoice-receiver'); ?></p>
        <?php
    }
    
    /**
     * Seller name callback
     */
    public function seller_name_callback() {
        $settings = get_option('leir_settings', array());
        $seller_name = isset($settings['seller_name']) ? $settings['seller_name'] : '';
        
        ?>
        <input type="text" id="leir_settings[seller_name]" name="leir_settings[seller_name]" value="<?php echo esc_attr($seller_name); ?>" class="regular-text">
        <p class="description"><?php _e('Enter the company name for the seller.', 'lhdn-e-invoice-receiver'); ?></p>
        <?php
    }
    
    /**
     * Seller TIN number callback
     */
    public function seller_tin_no_callback() {
        $settings = get_option('leir_settings', array());
        $seller_tin_no = isset($settings['seller_tin_no']) ? $settings['seller_tin_no'] : '';
        
        ?>
        <input type="text" id="leir_settings[seller_tin_no]" name="leir_settings[seller_tin_no]" value="<?php echo esc_attr($seller_tin_no); ?>" class="regular-text">
        <p class="description"><?php _e('Enter the Tax Identification Number (TIN) for the seller.', 'lhdn-e-invoice-receiver'); ?></p>
        <?php
    }
    
    /**
     * Seller ID type callback
     */
    public function seller_id_type_callback() {
        $settings = get_option('leir_settings', array());
        $seller_id_type = isset($settings['seller_id_type']) ? $settings['seller_id_type'] : '';
        
        ?>
        <input type="text" id="leir_settings[seller_id_type]" name="leir_settings[seller_id_type]" value="<?php echo esc_attr($seller_id_type); ?>" class="regular-text">
        <p class="description"><?php _e('Enter the ID type for the seller (e.g., Business Registration, ROC).', 'lhdn-e-invoice-receiver'); ?></p>
        <?php
    }
    
    /**
     * Seller ID value callback
     */
    public function seller_id_value_callback() {
        $settings = get_option('leir_settings', array());
        $seller_id_value = isset($settings['seller_id_value']) ? $settings['seller_id_value'] : '';
        
        ?>
        <input type="text" id="leir_settings[seller_id_value]" name="leir_settings[seller_id_value]" value="<?php echo esc_attr($seller_id_value); ?>" class="regular-text">
        <p class="description"><?php _e('Enter the ID value for the seller.', 'lhdn-e-invoice-receiver'); ?></p>
        <?php
    }
    
    /**
     * Seller contact number callback
     */
    public function seller_contact_no_callback() {
        $settings = get_option('leir_settings', array());
        $seller_contact_no = isset($settings['seller_contact_no']) ? $settings['seller_contact_no'] : '';
        
        ?>
        <input type="text" id="leir_settings[seller_contact_no]" name="leir_settings[seller_contact_no]" value="<?php echo esc_attr($seller_contact_no); ?>" class="regular-text">
        <p class="description"><?php _e('Enter the contact number for the seller.', 'lhdn-e-invoice-receiver'); ?></p>
        <?php
    }
    
    /**
     * Seller address callback
     */
    public function seller_address_callback() {
        $settings = get_option('leir_settings', array());
        $seller_address = isset($settings['seller_address']) ? $settings['seller_address'] : '';
        
        ?>
        <textarea id="leir_settings[seller_address]" name="leir_settings[seller_address]" rows="4" class="large-text"><?php echo esc_textarea($seller_address); ?></textarea>
        <p class="description"><?php _e('Enter the address for the seller.', 'lhdn-e-invoice-receiver'); ?></p>
        <?php
    }
    
    /**
     * Seller MSIC code callback
     */
    public function seller_msic_code_callback() {
        $settings = get_option('leir_settings', array());
        $seller_msic_code = isset($settings['seller_msic_code']) ? $settings['seller_msic_code'] : '';
        
        ?>
        <input type="text" id="leir_settings[seller_msic_code]" name="leir_settings[seller_msic_code]" value="<?php echo esc_attr($seller_msic_code); ?>" class="regular-text">
        <p class="description"><?php _e('Enter the Malaysia Standard Industrial Classification (MSIC) code for the seller.', 'lhdn-e-invoice-receiver'); ?></p>
        <?php
    }
    
    /**
     * Seller city name callback
     */
    public function seller_city_name_callback() {
        $settings = get_option('leir_settings', array());
        $seller_city_name = isset($settings['seller_city_name']) ? $settings['seller_city_name'] : '';
        
        ?>
        <input type="text" id="leir_settings[seller_city_name]" name="leir_settings[seller_city_name]" value="<?php echo esc_attr($seller_city_name); ?>" class="regular-text">
        <p class="description"><?php _e('Enter the city name for the seller.', 'lhdn-e-invoice-receiver'); ?></p>
        <?php
    }
    
    /**
     * Seller state code callback
     */
    public function seller_state_code_callback() {
        $settings = get_option('leir_settings', array());
        $seller_state_code = isset($settings['seller_state_code']) ? $settings['seller_state_code'] : '';
        
        ?>
        <input type="text" id="leir_settings[seller_state_code]" name="leir_settings[seller_state_code]" value="<?php echo esc_attr($seller_state_code); ?>" class="regular-text">
        <p class="description"><?php _e('Enter the state code for the seller.', 'lhdn-e-invoice-receiver'); ?></p>
        <?php
    }
    
    /**
     * Seller SST number callback
     */
    public function seller_sst_no_callback() {
        $settings = get_option('leir_settings', array());
        $seller_sst_no = isset($settings['seller_sst_no']) ? $settings['seller_sst_no'] : '';
        
        ?>
        <input type="text" id="leir_settings[seller_sst_no]" name="leir_settings[seller_sst_no]" value="<?php echo esc_attr($seller_sst_no); ?>" class="regular-text">
        <p class="description"><?php _e('Enter the Sales and Service Tax (SST) number for the seller.', 'lhdn-e-invoice-receiver'); ?></p>
        <?php
    }
    
    /**
     * Seller email callback
     */
    public function seller_email_callback() {
        $settings = get_option('leir_settings', array());
        $seller_email = isset($settings['seller_email']) ? $settings['seller_email'] : '';
        
        ?>
        <input type="email" id="leir_settings[seller_email]" name="leir_settings[seller_email]" value="<?php echo esc_attr($seller_email); ?>" class="regular-text">
        <p class="description"><?php _e('Enter the email address for the seller.', 'lhdn-e-invoice-receiver'); ?></p>
        <?php
    }
    
    /**
     * Seller branch name callback
     */
    public function seller_branch_name_callback() {
        $settings = get_option('leir_settings', array());
        $seller_branch_name = isset($settings['seller_branch_name']) ? $settings['seller_branch_name'] : '';
        
        ?>
        <input type="text" id="leir_settings[seller_branch_name]" name="leir_settings[seller_branch_name]" value="<?php echo esc_attr($seller_branch_name); ?>" class="regular-text">
        <p class="description"><?php _e('Enter the branch name for the seller.', 'lhdn-e-invoice-receiver'); ?></p>
        <?php
    }

    /**
     * Sanitize settings
     *
     * @param array $input The input array
     * @return array The sanitized array
     */
    public function sanitize_settings($input) {
        $sanitized = array();
        
        // Sanitize debug mode checkbox
        $sanitized['debug_mode'] = isset($input['debug_mode']) ? (bool) $input['debug_mode'] : false;
        
        // Update the separate option for removing data
        $remove_data = isset($_POST['leir_remove_data_on_uninstall']) ? (bool) $_POST['leir_remove_data_on_uninstall'] : false;
        update_option('leir_remove_data_on_uninstall', $remove_data);
        
        // Sanitize seller information fields
        // Text fields
        $text_fields = array(
            'seller_name',
            'seller_tin_no',
            'seller_id_type',
            'seller_id_value',
            'seller_contact_no',
            'seller_msic_code',
            'seller_city_name',
            'seller_state_code',
            'seller_sst_no',
            'seller_branch_name'
        );
        
        foreach ($text_fields as $field) {
            $sanitized[$field] = isset($input[$field]) ? sanitize_text_field($input[$field]) : '';
        }
        
        // Email field
        $sanitized['seller_email'] = isset($input['seller_email']) ? sanitize_email($input['seller_email']) : '';
        
        // Textarea field
        $sanitized['seller_address'] = isset($input['seller_address']) ? sanitize_textarea_field($input['seller_address']) : '';
        
        return $sanitized;
    }

    /**
     * Enqueue admin styles
     * 
     * @param string $hook The current admin page
     */
    public function enqueue_admin_styles($hook) {
        // Enqueue subscription styles for product edit page and subscription pages
        if ($hook === 'post.php' || $hook === 'post-new.php' || strpos($hook, 'lhdn-e-invoice') !== false) {
            $screen = get_current_screen();
            
            if ($screen && ($screen->post_type === 'product' || $screen->post_type === 'leir_subscription' || strpos($hook, 'lhdn-e-invoice') !== false)) {
                wp_enqueue_style(
                    'leir-subscription-admin-styles',
                    LEIR_PLUGIN_URL . 'assets/css/lhdn-e-invoice-subscriptions.css',
                    array(),
                    LEIR_VERSION
                );
            }
        }
    }

    /**
     * Handle subscription assignment form submission
     */
    public function handle_assign_subscription() {
        // Verify nonce
        if (!isset($_POST['leir_assign_subscription_nonce']) || 
            !wp_verify_nonce($_POST['leir_assign_subscription_nonce'], 'leir_assign_subscription')) {
            wp_die(__('Security check failed.', 'lhdn-e-invoice-receiver'));
        }
        
        // Check capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have permission to perform this action.', 'lhdn-e-invoice-receiver'));
        }
        
        // Sanitize and validate input
        $user_id = intval($_POST['user_id']);
        $duration = intval($_POST['duration']);
        $start_date = sanitize_text_field($_POST['start_date']);
        $status = sanitize_text_field($_POST['status']);
        $notes = sanitize_textarea_field($_POST['notes']);
        
        // Validate user
        $user = get_userdata($user_id);
        if (!$user) {
            $redirect_url = add_query_arg('message', 'invalid_user', admin_url('admin.php?page=lhdn-e-invoice-user-subscriptions'));
            wp_redirect($redirect_url);
            exit;
        }
        
        // Validate duration
        if ($duration < 1 || $duration > 60) {
            $duration = 12; // Default to 12 months
        }
        
        // Validate status
        if (!in_array($status, array('active', 'pending'))) {
            $status = 'active';
        }
        
        // Validate start date
        if (!$start_date || !strtotime($start_date)) {
            $start_date = date('Y-m-d');
        }
        
        // Calculate expiry date
        $start_datetime = date('Y-m-d H:i:s', strtotime($start_date));
        $expiry_datetime = date('Y-m-d H:i:s', strtotime("+{$duration} months", strtotime($start_datetime)));
        
        // Check if user already has an active subscription
        $existing_subscription = $this->get_user_active_subscription($user_id);
        if ($existing_subscription && $status === 'active') {
            // Optionally extend the existing subscription instead of creating a new one
            $current_expiry = get_post_meta($existing_subscription->ID, '_leir_expiry_date', true);
            if ($current_expiry && strtotime($current_expiry) > time()) {
                // Extend from current expiry date
                $expiry_datetime = date('Y-m-d H:i:s', strtotime("+{$duration} months", strtotime($current_expiry)));
                update_post_meta($existing_subscription->ID, '_leir_expiry_date', $expiry_datetime);
                
                // Add admin notes
                if ($notes) {
                    $existing_notes = get_post_meta($existing_subscription->ID, '_leir_admin_notes', true);
                    $updated_notes = $existing_notes ? $existing_notes . "\n\n" . date('Y-m-d H:i:s') . " - Extended: " . $notes : $notes;
                    update_post_meta($existing_subscription->ID, '_leir_admin_notes', $updated_notes);
                }
                
                $redirect_url = add_query_arg('message', 'assigned', admin_url('admin.php?page=lhdn-e-invoice-user-subscriptions'));
                wp_redirect($redirect_url);
                exit;
            }
        }
        
        // Create new subscription
        $subscription_data = array(
            'post_title' => sprintf(__('Admin Subscription #%s for %s', 'lhdn-e-invoice-receiver'), uniqid(), $user->display_name),
            'post_status' => 'publish',
            'post_type' => 'leir_subscription',
            'post_author' => get_current_user_id()
        );
        
        $subscription_id = wp_insert_post($subscription_data);
        
        if (is_wp_error($subscription_id)) {
            error_log('Failed to create admin subscription: ' . $subscription_id->get_error_message());
            $redirect_url = add_query_arg('message', 'error', admin_url('admin.php?page=lhdn-e-invoice-user-subscriptions'));
            wp_redirect($redirect_url);
            exit;
        }
        
        // Save subscription metadata
        update_post_meta($subscription_id, '_leir_user_id', $user_id);
        update_post_meta($subscription_id, '_leir_start_date', $start_datetime);
        update_post_meta($subscription_id, '_leir_expiry_date', $expiry_datetime);
        update_post_meta($subscription_id, '_leir_status', $status);
        update_post_meta($subscription_id, '_leir_admin_assigned', 1);
        update_post_meta($subscription_id, '_leir_assigned_by', get_current_user_id());
        update_post_meta($subscription_id, '_leir_duration_months', $duration);
        
        if ($notes) {
            update_post_meta($subscription_id, '_leir_admin_notes', $notes);
        }
        
        // Schedule renewal reminders if active (30 days before expiry)
        if ($status === 'active') {
            $reminder_time = strtotime("-30 days", strtotime($expiry_datetime));
            if ($reminder_time > time()) {
                wp_schedule_single_event($reminder_time, 'leir_send_renewal_reminder', array($subscription_id));
            }
        }
        
        // Log the assignment
        error_log(sprintf(
            'Admin subscription assigned: ID %s, User %s (%s), Duration %s months, Status %s, By User %s',
            $subscription_id,
            $user->display_name,
            $user->user_email,
            $duration,
            $status,
            wp_get_current_user()->display_name
        ));
        
        // Trigger action for other integrations
        do_action('leir_admin_subscription_assigned', $subscription_id, $user_id, $duration, $status);
        
        // Redirect with success message
        $redirect_url = add_query_arg('message', 'assigned', admin_url('admin.php?page=lhdn-e-invoice-user-subscriptions'));
        wp_redirect($redirect_url);
        exit;
    }
    
    /**
     * Get user's active subscription (helper method)
     * 
     * @param int $user_id The user ID
     * @return object|null The subscription post object or null if none found
     */
    private function get_user_active_subscription($user_id) {
        if (empty($user_id)) {
            return null;
        }
        
        $args = array(
            'post_type' => 'leir_subscription',
            'posts_per_page' => 1,
            'meta_query' => array(
                'relation' => 'AND',
                array(
                    'key' => '_leir_user_id',
                    'value' => $user_id,
                    'compare' => '='
                ),
                array(
                    'key' => '_leir_status',
                    'value' => 'active',
                    'compare' => '='
                ),
                array(
                    'key' => '_leir_expiry_date',
                    'value' => current_time('mysql'),
                    'compare' => '>',
                    'type' => 'DATETIME'
                )
            )
        );
        
        $subscription_query = new WP_Query($args);
        
        if ($subscription_query->have_posts()) {
            return $subscription_query->posts[0];
        }
        
        return null;
    }
    
    /**
     * Get current settings
     */
} 