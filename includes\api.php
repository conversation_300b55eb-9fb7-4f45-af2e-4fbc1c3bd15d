<?php
/**
 * API functionality for E-Invoice Receiver
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register API endpoints
 */
function leir_register_api_endpoints() {
    // Register the REST API route for receiving e-invoices
    register_rest_route('lhdn-e-invoice-receiver/v1', '/receive-einvoice', array(
        'methods' => 'POST',
        'callback' => 'leir_receive_einvoice_callback',
        'permission_callback' => 'leir_check_token_auth'
    ));
}
add_action('rest_api_init', 'leir_register_api_endpoints');

/**
 * Check token-based authentication
 *
 * @param WP_REST_Request $request The request object
 * @return bool|WP_Error Whether the request is authenticated
 */
function leir_check_token_auth($request) {
    $auth_header = $request->get_header('Authorization');
    
    if (empty($auth_header)) {
        return new WP_Error(
            'authorization_required',
            __('Authorization header is required', 'lhdn-e-invoice-receiver'),
            array('status' => 401)
        );
    }
    
    // Extract the token from the header
    // Expected format: "Bearer TOKEN" or custom format
    if (strpos($auth_header, 'Bearer ') === 0) {
        $token = substr($auth_header, 7);
    } else {
        $token = $auth_header;
    }
    
    $token = sanitize_text_field($token);
    
    // Verify the token
    global $wpdb;
    $user_id = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT user_id FROM {$wpdb->prefix}leir_tokens WHERE token = %s LIMIT 1",
            $token
        )
    );
    
    if (!$user_id) {
        return new WP_Error(
            'invalid_token',
            __('Invalid authorization token', 'lhdn-e-invoice-receiver'),
            array('status' => 401)
        );
    }
    
    // Check if user has an active subscription (skip for administrators)
    if (!current_user_can('manage_options') && !user_can($user_id, 'manage_options')) {
        if (!function_exists('leir_user_has_active_subscription')) {
            require_once LEIR_PLUGIN_DIR . 'includes/helpers.php';
        }
        
        if (!leir_user_has_active_subscription($user_id)) {
            return new WP_Error(
                'subscription_required',
                __('An active subscription is required to use this API', 'lhdn-e-invoice-receiver'),
                array('status' => 403)
            );
        }
    }
    
    // Set the user_id in the request for use in the callback
    $request->set_param('user_id', $user_id);
    
    return true;
}

/**
 * Callback function for receiving e-invoice data
 *
 * @param WP_REST_Request $request The request object
 * @return WP_REST_Response The response
 */
function leir_receive_einvoice_callback($request) {
    // Check content type
    $content_type = $request->get_header('Content-Type');
    if (strpos($content_type, 'application/json') === false) {
        return new WP_REST_Response(
            array(
                'success' => false,
                'error' => 'Content-Type must be application/json'
            ),
            400
        );
    }
    
    // Get the raw JSON body
    $body = $request->get_body();
    
    // Parse the JSON
    $invoice_data = json_decode($body, true);
    
    // Check if JSON parsing failed
    if ($invoice_data === null && json_last_error() !== JSON_ERROR_NONE) {
        return new WP_REST_Response(
            array(
                'success' => false,
                'error' => 'Invalid JSON: ' . json_last_error_msg()
            ),
            400
        );
    }
    
    // Get the user ID from the request parameter (set in auth check)
    $user_id = $request->get_param('user_id');
    
    // Process the invoice data
    $receiver = new LEIR_E_Invoice_Receiver();
    $result = $receiver->process_invoice($invoice_data, $user_id);
    
    // Return success or error response
    if (!empty($result['success'])) {
        return new WP_REST_Response($result, 200);
    } else {
        $status_code = 400; // Default for validation errors
        
        // Check for specific error types
        if (strpos($result['error'], 'Internal server error') !== false) {
            $status_code = 500;
        }
        
        return new WP_REST_Response($result, $status_code);
    }
}

/**
 * Get the full API endpoint URL
 *
 * @return string The API endpoint URL
 */
function leir_get_api_endpoint_url() {
    return rest_url('lhdn-e-invoice-receiver/v1/receive-einvoice');
}

/**
 * Delete a user's token
 *
 * @param int $user_id The user ID
 * @param string $token The token to delete
 * @return bool Whether the deletion was successful
 */
function leir_delete_user_token($user_id, $token) {
    global $wpdb;
    return $wpdb->delete(
        $wpdb->prefix . 'leir_tokens',
        array(
            'user_id' => $user_id,
            'token' => $token
        ),
        array(
            '%d',
            '%s'
        )
    );
}

/**
 * Get all tokens for a user
 *
 * @param int $user_id The user ID
 * @return array The user's tokens
 */
function leir_get_user_tokens($user_id) {
    global $wpdb;
    
    if (empty($user_id) || !is_numeric($user_id)) {
        error_log('Invalid user ID for token retrieval: ' . var_export($user_id, true));
        return array();
    }
    
    $table_name = $wpdb->prefix . 'leir_tokens';
    
    // Check if the table exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'");
    if (!$table_exists) {
        error_log("Table {$table_name} does not exist");
        return array();
    }
    
    $query = $wpdb->prepare(
        "SELECT id, token, website_name, created FROM {$table_name} WHERE user_id = %d ORDER BY created DESC",
        $user_id
    );
    
    error_log("Executing token query: {$query}");
    
    $tokens = $wpdb->get_results($query);
    
    if ($wpdb->last_error) {
        error_log("Database error when fetching tokens: {$wpdb->last_error}");
    }
    
    $token_count = is_array($tokens) ? count($tokens) : 0;
    error_log("Retrieved {$token_count} tokens for user ID {$user_id}");
    
    return $tokens ? $tokens : array();
} 