<?php
/**
 * Main class for E-Invoice Receiver functionality
 */
class LEIR_E_Invoice_Receiver {
    /**
     * Constructor
     */
    public function __construct() {
        // Nothing to initialize here
    }
    
    /**
     * Process the received invoice data
     *
     * @param array $invoice_data The invoice data to process
     * @param int $user_id The user ID associated with the token
     * @return array Response data
     */
    public function process_invoice($invoice_data, $user_id) {
        global $wpdb;
        
        // Verify user has an active subscription (skip for admins)
        if (!user_can($user_id, 'manage_options')) {
            if (!leir_user_has_active_subscription($user_id)) {
                return [
                    'success' => false,
                    'error' => 'Active subscription required to process e-invoices.'
                ];
            }
        }
        
        // Extract the first invoice from the array (currently only one is expected)
        if (!is_array($invoice_data) || empty($invoice_data) || !isset($invoice_data[0])) {
            return [
                'success' => false, 
                'error' => 'Invalid invoice data format. Expected an array with at least one invoice object.'
            ];
        }
        
        $invoice = $invoice_data[0];
        
        // Add seller information from plugin settings if not already present
        $invoice = $this->add_seller_information($invoice);
        
        // Validate the invoice data
        $validation = $this->validate_invoice($invoice);
        if (!$validation['valid']) {
            return [
                'success' => false,
                'error' => 'Validation failed: ' . $validation['message']
            ];
        }
        
        // Check if this invoice has already been processed (idempotency check)
        $transaction_code = sanitize_text_field($invoice['TransactionCode']);
        $order_id = absint($invoice['OrderID']);
        
        $existing = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT id FROM {$wpdb->prefix}leir_invoices WHERE transaction_code = %s",
                $transaction_code
            )
        );
        
        if ($existing) {
            return [
                'success' => true,
                'message' => 'E-Invoice data already received and processed.',
                'referenceId' => $existing
            ];
        }
        
        // Store the invoice in the database
        $result = $wpdb->insert(
            $wpdb->prefix . 'leir_invoices',
            [
                'user_id' => $user_id,
                'transaction_code' => $transaction_code,
                'order_id' => $order_id,
                'invoice_data' => wp_json_encode($invoice),
                'received_date' => current_time('mysql')
            ],
            [
                '%d',
                '%s',
                '%d',
                '%s',
                '%s'
            ]
        );
        
        if ($result === false) {
            return [
                'success' => false,
                'error' => 'Internal server error occurred while saving invoice data.'
            ];
        }
        
        $invoice_id = $wpdb->insert_id;
        
        // Hook for additional processing
        do_action('leir_invoice_received', $invoice, $invoice_id, $user_id);
        
        return [
            'success' => true,
            'message' => 'E-Invoice data received successfully.',
            'referenceId' => $invoice_id
        ];
    }
    
    /**
     * Add seller information from plugin settings to the invoice
     *
     * @param array $invoice The invoice data
     * @return array The invoice data with seller information added
     */
    private function add_seller_information($invoice) {
        // Get seller information from settings
        $settings = get_option('leir_settings', array());
        
        // Map settings to invoice fields
        $seller_fields = array(
            'SellerName' => 'seller_name',
            'SellerTinNo' => 'seller_tin_no',
            'SellerIdType' => 'seller_id_type',
            'SellerIdValue' => 'seller_id_value',
            'SellerSstNo' => 'seller_sst_no',
            'SellerEmail' => 'seller_email',
            'SellerAddress' => 'seller_address',
            'SellerCityName' => 'seller_city_name',
            'SellerStateCode' => 'seller_state_code',
            'SellerContactNo' => 'seller_contact_no',
            'SellerBranchName' => 'seller_branch_name',
            'SellerMsicCode' => 'seller_msic_code'
        );
        
        // Add seller fields to invoice if they don't already exist
        foreach ($seller_fields as $invoice_field => $setting_field) {
            if (!isset($invoice[$invoice_field]) || empty($invoice[$invoice_field])) {
                $invoice[$invoice_field] = isset($settings[$setting_field]) ? $settings[$setting_field] : '';
            }
        }
        
        return $invoice;
    }
    
    /**
     * Validate the invoice data
     *
     * @param array $invoice The invoice data to validate
     * @return array Validation result with valid flag and message
     */
    private function validate_invoice($invoice) {
        // Required fields
        $required_fields = [
            'OrderID',
            'TransactionCode',
            'TransactionDate',
            'BuyerName',
            'TotalIncludingTax',
            'Items',
            'SellerTinNo'
        ];
        
        foreach ($required_fields as $field) {
            if (!isset($invoice[$field]) || empty($invoice[$field])) {
                return [
                    'valid' => false,
                    'message' => "Missing required field '$field'."
                ];
            }
        }
        
        // Check if Items is an array
        if (!is_array($invoice['Items']) || empty($invoice['Items'])) {
            return [
                'valid' => false,
                'message' => 'Items must be a non-empty array.'
            ];
        }
        
        // Check if Payments is an array
        if (!isset($invoice['Payments']) || !is_array($invoice['Payments']) || empty($invoice['Payments'])) {
            return [
                'valid' => false,
                'message' => 'Payments must be a non-empty array.'
            ];
        }
        
        // Validate date format
        if (!$this->validate_date_format($invoice['TransactionDate'], 'Y-m-d')) {
            return [
                'valid' => false,
                'message' => 'TransactionDate must be in YYYY-MM-DD format.'
            ];
        }
        
        // Validate time format if present
        if (isset($invoice['TransactionTime']) && !empty($invoice['TransactionTime'])) {
            if (!$this->validate_date_format($invoice['TransactionTime'], 'H:i:s')) {
                return [
                    'valid' => false,
                    'message' => 'TransactionTime must be in HH:MM:SS format.'
                ];
            }
        }
        
        // Validate numeric fields
        $numeric_fields = [
            'TotalTaxAmount',
            'TotalIncludingTax',
            'TotalExcludingTax',
            'TotalSubtotalAmount',
            'TotalPayableAmount',
            'TotalDiscount'
        ];
        
        foreach ($numeric_fields as $field) {
            if (isset($invoice[$field]) && !is_numeric($invoice[$field])) {
                return [
                    'valid' => false,
                    'message' => "Field '$field' must be numeric."
                ];
            }
        }
        
        return [
            'valid' => true,
            'message' => 'Validation passed.'
        ];
    }
    
    /**
     * Validate date format
     *
     * @param string $date The date string to validate
     * @param string $format The expected format
     * @return bool Whether the date is valid
     */
    private function validate_date_format($date, $format) {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
} 