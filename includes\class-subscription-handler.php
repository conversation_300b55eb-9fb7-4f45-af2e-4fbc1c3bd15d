<?php
/**
 * Subscription Handler Class
 * 
 * Manages annual subscriptions through WooCommerce checkout
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class for managing subscriptions
 */
class LEIR_Subscription_Handler {

    /**
     * Constructor
     */
    public function __construct() {
        // Add actions and filters for subscription functionality
        add_action('init', array($this, 'register_subscription_post_type'));
        add_action('woocommerce_checkout_update_order_meta', array($this, 'save_subscription_data'), 10, 2);
        add_action('woocommerce_order_status_completed', array($this, 'activate_subscription'));
        add_action('woocommerce_payment_complete', array($this, 'activate_subscription'));
        
        // Add subscription plan metabox to products
        add_action('add_meta_boxes', array($this, 'add_subscription_meta_box'));
        add_action('save_post', array($this, 'save_subscription_meta_box_data'));
        
        // Add subscription information to order display
        add_action('woocommerce_order_details_after_order_table', array($this, 'display_subscription_details'));
        add_action('woocommerce_admin_order_data_after_billing_address', array($this, 'display_admin_subscription_details'));
        
        // Create subscription tab in My Account
        add_filter('woocommerce_account_menu_items', array($this, 'add_subscription_menu_item'));
        add_action('init', array($this, 'add_subscription_endpoint'));
        add_action('woocommerce_account_subscriptions_endpoint', array($this, 'subscription_content'));
        
        // Schedule daily check for expired subscriptions
        if (!wp_next_scheduled('leir_check_expired_subscriptions')) {
            wp_schedule_event(time(), 'daily', 'leir_check_expired_subscriptions');
        }
        add_action('leir_check_expired_subscriptions', array($this, 'check_expired_subscriptions'));
        
        // Add renewal reminders
        add_action('leir_send_renewal_reminder', array($this, 'send_renewal_reminder'));
    }
    
    /**
     * Register the subscription post type
     */
    public function register_subscription_post_type() {
        $labels = array(
            'name' => __('Subscriptions', 'lhdn-e-invoice-receiver'),
            'singular_name' => __('Subscription', 'lhdn-e-invoice-receiver'),
            'menu_name' => __('Subscriptions', 'lhdn-e-invoice-receiver'),
            'all_items' => __('All Subscriptions', 'lhdn-e-invoice-receiver'),
            'view_item' => __('View Subscription', 'lhdn-e-invoice-receiver'),
            'add_new_item' => __('Add New Subscription', 'lhdn-e-invoice-receiver'),
            'add_new' => __('Add New', 'lhdn-e-invoice-receiver'),
            'edit_item' => __('Edit Subscription', 'lhdn-e-invoice-receiver'),
            'update_item' => __('Update Subscription', 'lhdn-e-invoice-receiver'),
            'search_items' => __('Search Subscription', 'lhdn-e-invoice-receiver'),
            'not_found' => __('Not found', 'lhdn-e-invoice-receiver'),
            'not_found_in_trash' => __('Not found in Trash', 'lhdn-e-invoice-receiver')
        );
        
        $args = array(
            'label' => __('Subscription', 'lhdn-e-invoice-receiver'),
            'description' => __('E-Invoice Subscription Plan', 'lhdn-e-invoice-receiver'),
            'labels' => $labels,
            'supports' => array('title'),
            'hierarchical' => false,
            'public' => false,
            'show_ui' => true,
            'show_in_menu' => 'lhdn-e-invoice-receiver',
            'can_export' => true,
            'has_archive' => false,
            'exclude_from_search' => true,
            'publicly_queryable' => false,
            'capability_type' => 'post',
            'show_in_rest' => false,
        );
        
        register_post_type('leir_subscription', $args);
    }
    
    /**
     * Add meta box for subscription settings to product edit page
     */
    public function add_subscription_meta_box() {
        add_meta_box(
            'leir_subscription_meta_box',
            __('Subscription Settings', 'lhdn-e-invoice-receiver'),
            array($this, 'render_subscription_meta_box'),
            'product',
            'normal',
            'high'
        );
    }
    
    /**
     * Render the subscription meta box
     * 
     * @param WP_Post $post The post object
     */
    public function render_subscription_meta_box($post) {
        // Add nonce for security
        wp_nonce_field('leir_subscription_meta_box', 'leir_subscription_meta_box_nonce');
        
        // Get current values
        $is_subscription = get_post_meta($post->ID, '_leir_is_subscription', true);
        $duration = get_post_meta($post->ID, '_leir_subscription_duration', true);
        $duration = !empty($duration) ? $duration : 12; // Default to 12 months
        
        // Output fields
        ?>
        <p>
            <input type="checkbox" id="leir_is_subscription" name="leir_is_subscription" value="1" <?php checked($is_subscription, 1); ?> />
            <label for="leir_is_subscription"><?php _e('This product is an E-Invoice subscription plan', 'lhdn-e-invoice-receiver'); ?></label>
        </p>
        
        <p>
            <label for="leir_subscription_duration"><?php _e('Subscription Duration (months):', 'lhdn-e-invoice-receiver'); ?></label>
            <input type="number" id="leir_subscription_duration" name="leir_subscription_duration" value="<?php echo esc_attr($duration); ?>" min="1" max="60" step="1" />
        </p>
        
        <p class="description">
            <?php _e('Set this product as a subscription plan to provide access to the E-Invoice service for the specified duration.', 'lhdn-e-invoice-receiver'); ?>
        </p>
        <?php
    }
    
    /**
     * Save subscription meta box data
     * 
     * @param int $post_id The post ID
     */
    public function save_subscription_meta_box_data($post_id) {
        // Check if our nonce is set
        if (!isset($_POST['leir_subscription_meta_box_nonce'])) {
            return;
        }
        
        // Verify the nonce
        if (!wp_verify_nonce($_POST['leir_subscription_meta_box_nonce'], 'leir_subscription_meta_box')) {
            return;
        }
        
        // If this is an autosave, our form has not been submitted
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        // Check the user's permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // Save subscription data
        $is_subscription = isset($_POST['leir_is_subscription']) ? 1 : 0;
        update_post_meta($post_id, '_leir_is_subscription', $is_subscription);
        
        if (isset($_POST['leir_subscription_duration'])) {
            $duration = intval($_POST['leir_subscription_duration']);
            $duration = max(1, min(60, $duration)); // Limit between 1 and 60 months
            update_post_meta($post_id, '_leir_subscription_duration', $duration);
        }
    }
    
    /**
     * Save subscription data during checkout
     * 
     * @param int $order_id The order ID
     * @param array $posted_data The posted data
     */
    public function save_subscription_data($order_id, $posted_data) {
        $order = wc_get_order($order_id);
        $has_subscription = false;
        
        // Check each product in the order
        foreach ($order->get_items() as $item) {
            $product_id = $item->get_product_id();
            $is_subscription = get_post_meta($product_id, '_leir_is_subscription', true);
            
            if ($is_subscription) {
                $has_subscription = true;
                $duration = get_post_meta($product_id, '_leir_subscription_duration', true);
                
                // Store subscription data in order meta
                update_post_meta($order_id, '_leir_has_subscription', 1);
                update_post_meta($order_id, '_leir_subscription_product_id', $product_id);
                update_post_meta($order_id, '_leir_subscription_duration', $duration);
                
                // Only process the first subscription product found
                break;
            }
        }
    }
    
    /**
     * Activate subscription when order is completed or payment is processed
     * 
     * @param int $order_id The order ID
     */
    public function activate_subscription($order_id) {
        // Check if order has subscription
        $has_subscription = get_post_meta($order_id, '_leir_has_subscription', true);
        
        if (!$has_subscription) {
            return;
        }
        
        // Get subscription details
        $order = wc_get_order($order_id);
        $user_id = $order->get_user_id();
        $product_id = get_post_meta($order_id, '_leir_subscription_product_id', true);
        $duration = get_post_meta($order_id, '_leir_subscription_duration', true);
        
        // Calculate expiration date (current time + duration in months)
        $start_date = current_time('mysql');
        $expiry_date = date('Y-m-d H:i:s', strtotime("+{$duration} months", strtotime($start_date)));
        
        // Create new subscription post
        $subscription_id = wp_insert_post(array(
            'post_title' => sprintf(__('Subscription #%s for User #%s', 'lhdn-e-invoice-receiver'), uniqid(), $user_id),
            'post_status' => 'publish',
            'post_type' => 'leir_subscription',
            'post_author' => $user_id
        ));
        
        if (is_wp_error($subscription_id)) {
            error_log('Failed to create subscription: ' . $subscription_id->get_error_message());
            return;
        }
        
        // Store subscription meta
        update_post_meta($subscription_id, '_leir_user_id', $user_id);
        update_post_meta($subscription_id, '_leir_order_id', $order_id);
        update_post_meta($subscription_id, '_leir_product_id', $product_id);
        update_post_meta($subscription_id, '_leir_start_date', $start_date);
        update_post_meta($subscription_id, '_leir_expiry_date', $expiry_date);
        update_post_meta($subscription_id, '_leir_status', 'active');
        
        // Link subscription to order
        update_post_meta($order_id, '_leir_subscription_id', $subscription_id);
        
        // Schedule renewal reminders (30 days before expiry)
        $reminder_time = strtotime("-30 days", strtotime($expiry_date));
        if ($reminder_time > time()) {
            wp_schedule_single_event($reminder_time, 'leir_send_renewal_reminder', array($subscription_id));
        }
        
        // Log activation
        error_log(sprintf('Subscription #%s activated for User #%s with expiry on %s', $subscription_id, $user_id, $expiry_date));
        
        // Trigger action for other integrations
        do_action('leir_subscription_activated', $subscription_id, $user_id, $order_id);
    }
    
    /**
     * Display subscription details on order page
     * 
     * @param WC_Order $order The order object
     */
    public function display_subscription_details($order) {
        $order_id = $order->get_id();
        $has_subscription = get_post_meta($order_id, '_leir_has_subscription', true);
        
        if (!$has_subscription) {
            return;
        }
        
        $subscription_id = get_post_meta($order_id, '_leir_subscription_id', true);
        
        if (!$subscription_id) {
            return;
        }
        
        $start_date = get_post_meta($subscription_id, '_leir_start_date', true);
        $expiry_date = get_post_meta($subscription_id, '_leir_expiry_date', true);
        $status = get_post_meta($subscription_id, '_leir_status', true);
        
        echo '<h2>' . __('Subscription Details', 'lhdn-e-invoice-receiver') . '</h2>';
        echo '<table class="woocommerce-table">';
        echo '<tr><th>' . __('Subscription Status', 'lhdn-e-invoice-receiver') . '</th><td>' . ucfirst($status) . '</td></tr>';
        echo '<tr><th>' . __('Start Date', 'lhdn-e-invoice-receiver') . '</th><td>' . date_i18n(get_option('date_format'), strtotime($start_date)) . '</td></tr>';
        echo '<tr><th>' . __('Expiry Date', 'lhdn-e-invoice-receiver') . '</th><td>' . date_i18n(get_option('date_format'), strtotime($expiry_date)) . '</td></tr>';
        echo '</table>';
    }
    
    /**
     * Display subscription details in admin order page
     * 
     * @param WC_Order $order The order object
     */
    public function display_admin_subscription_details($order) {
        $order_id = $order->get_id();
        $has_subscription = get_post_meta($order_id, '_leir_has_subscription', true);
        
        if (!$has_subscription) {
            return;
        }
        
        $subscription_id = get_post_meta($order_id, '_leir_subscription_id', true);
        
        if (!$subscription_id) {
            echo '<div class="subscription-data">';
            echo '<h3>' . __('E-Invoice Subscription', 'lhdn-e-invoice-receiver') . '</h3>';
            echo '<p>' . __('This order contains a subscription that has not been activated yet.', 'lhdn-e-invoice-receiver') . '</p>';
            echo '</div>';
            return;
        }
        
        $start_date = get_post_meta($subscription_id, '_leir_start_date', true);
        $expiry_date = get_post_meta($subscription_id, '_leir_expiry_date', true);
        $status = get_post_meta($subscription_id, '_leir_status', true);
        
        echo '<div class="subscription-data">';
        echo '<h3>' . __('E-Invoice Subscription', 'lhdn-e-invoice-receiver') . '</h3>';
        echo '<p><strong>' . __('Subscription ID:', 'lhdn-e-invoice-receiver') . '</strong> ' . $subscription_id . '</p>';
        echo '<p><strong>' . __('Status:', 'lhdn-e-invoice-receiver') . '</strong> ' . ucfirst($status) . '</p>';
        echo '<p><strong>' . __('Start Date:', 'lhdn-e-invoice-receiver') . '</strong> ' . date_i18n(get_option('date_format'), strtotime($start_date)) . '</p>';
        echo '<p><strong>' . __('Expiry Date:', 'lhdn-e-invoice-receiver') . '</strong> ' . date_i18n(get_option('date_format'), strtotime($expiry_date)) . '</p>';
        echo '<p><a href="' . admin_url('post.php?post=' . $subscription_id . '&action=edit') . '">' . __('View/Edit Subscription', 'lhdn-e-invoice-receiver') . '</a></p>';
        echo '</div>';
    }
    
    /**
     * Add subscription endpoint to My Account
     */
    public function add_subscription_endpoint() {
        add_rewrite_endpoint('subscriptions', EP_ROOT | EP_PAGES);
    }
    
    /**
     * Add subscription menu item to My Account
     * 
     * @param array $items Menu items
     * @return array Modified menu items
     */
    public function add_subscription_menu_item($items) {
        // Insert the Subscriptions tab after Dashboard but before Orders
        $new_items = array();
        
        foreach ($items as $key => $value) {
            $new_items[$key] = $value;
            
            if ($key === 'dashboard') {
                $new_items['subscriptions'] = __('My Subscriptions', 'lhdn-e-invoice-receiver');
            }
        }
        
        return $new_items;
    }
    
    /**
     * Display subscription content in My Account
     */
    public function subscription_content() {
        $user_id = get_current_user_id();
        
        // Get user's subscriptions
        $subscriptions = $this->get_user_subscriptions($user_id);
        
        echo '<h2>' . __('My Subscriptions', 'lhdn-e-invoice-receiver') . '</h2>';
        
        if (empty($subscriptions)) {
            echo '<p>' . __('You don\'t have any active subscriptions.', 'lhdn-e-invoice-receiver') . '</p>';
            
            // Show link to subscription products
            echo '<p><a href="' . esc_url(wc_get_page_permalink('shop')) . '" class="button">' . __('Browse Subscription Plans', 'lhdn-e-invoice-receiver') . '</a></p>';
            return;
        }
        
        echo '<table class="woocommerce-orders-table woocommerce-MyAccount-subscriptions shop_table shop_table_responsive">';
        echo '<thead><tr>';
        echo '<th>' . __('Subscription', 'lhdn-e-invoice-receiver') . '</th>';
        echo '<th>' . __('Start Date', 'lhdn-e-invoice-receiver') . '</th>';
        echo '<th>' . __('Expiry Date', 'lhdn-e-invoice-receiver') . '</th>';
        echo '<th>' . __('Status', 'lhdn-e-invoice-receiver') . '</th>';
        echo '<th>' . __('Actions', 'lhdn-e-invoice-receiver') . '</th>';
        echo '</tr></thead>';
        echo '<tbody>';
        
        foreach ($subscriptions as $subscription) {
            $subscription_id = $subscription->ID;
            $product_id = get_post_meta($subscription_id, '_leir_product_id', true);
            $order_id = get_post_meta($subscription_id, '_leir_order_id', true);
            $start_date = get_post_meta($subscription_id, '_leir_start_date', true);
            $expiry_date = get_post_meta($subscription_id, '_leir_expiry_date', true);
            $status = get_post_meta($subscription_id, '_leir_status', true);
            
            $product_name = '';
            if ($product_id) {
                $product = wc_get_product($product_id);
                if ($product) {
                    $product_name = $product->get_name();
                }
            }
            
            echo '<tr>';
            echo '<td>' . esc_html($product_name) . '</td>';
            echo '<td>' . date_i18n(get_option('date_format'), strtotime($start_date)) . '</td>';
            echo '<td>' . date_i18n(get_option('date_format'), strtotime($expiry_date)) . '</td>';
            echo '<td>' . ucfirst($status) . '</td>';
            echo '<td>';
            
            if ($order_id) {
                echo '<a href="' . esc_url(wc_get_endpoint_url('view-order', $order_id, wc_get_page_permalink('myaccount'))) . '" class="button">' . __('View Order', 'lhdn-e-invoice-receiver') . '</a> ';
            }
            
            // If subscription is active and expiring within 30 days, show renewal button
            if ($status === 'active' && strtotime($expiry_date) - time() < 30 * DAY_IN_SECONDS) {
                echo '<a href="' . esc_url(add_query_arg('renew_subscription', $subscription_id, wc_get_endpoint_url('subscriptions'))) . '" class="button">' . __('Renew', 'lhdn-e-invoice-receiver') . '</a>';
            }
            
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody></table>';
    }
    
    /**
     * Get subscriptions for a user
     * 
     * @param int $user_id The user ID
     * @return array The subscriptions
     */
    public function get_user_subscriptions($user_id) {
        $args = array(
            'post_type' => 'leir_subscription',
            'posts_per_page' => -1,
            'meta_query' => array(
                array(
                    'key' => '_leir_user_id',
                    'value' => $user_id,
                    'compare' => '='
                )
            )
        );
        
        return get_posts($args);
    }
    
    /**
     * Check for expired subscriptions
     */
    public function check_expired_subscriptions() {
        $args = array(
            'post_type' => 'leir_subscription',
            'posts_per_page' => -1,
            'meta_query' => array(
                'relation' => 'AND',
                array(
                    'key' => '_leir_status',
                    'value' => 'active',
                    'compare' => '='
                ),
                array(
                    'key' => '_leir_expiry_date',
                    'value' => current_time('mysql'),
                    'compare' => '<',
                    'type' => 'DATETIME'
                )
            )
        );
        
        $expired_subscriptions = get_posts($args);
        
        foreach ($expired_subscriptions as $subscription) {
            $subscription_id = $subscription->ID;
            
            // Update status to expired
            update_post_meta($subscription_id, '_leir_status', 'expired');
            
            // Get user ID
            $user_id = get_post_meta($subscription_id, '_leir_user_id', true);
            
            // Log expiration
            error_log(sprintf('Subscription #%s for User #%s has expired', $subscription_id, $user_id));
            
            // Trigger action for other integrations
            do_action('leir_subscription_expired', $subscription_id, $user_id);
            
            // Send expiration email
            $this->send_expiration_email($subscription_id, $user_id);
        }
    }
    
    /**
     * Send subscription expiration email
     * 
     * @param int $subscription_id The subscription ID
     * @param int $user_id The user ID
     */
    private function send_expiration_email($subscription_id, $user_id) {
        $user = get_userdata($user_id);
        
        if (!$user) {
            return;
        }
        
        $product_id = get_post_meta($subscription_id, '_leir_product_id', true);
        $product_name = '';
        
        if ($product_id) {
            $product = wc_get_product($product_id);
            if ($product) {
                $product_name = $product->get_name();
            }
        }
        
        $subject = sprintf(__('Your %s subscription has expired', 'lhdn-e-invoice-receiver'), get_bloginfo('name'));
        
        $message = sprintf(__('Hello %s,', 'lhdn-e-invoice-receiver'), $user->display_name) . "\n\n";
        $message .= sprintf(__('Your subscription to %s has expired.', 'lhdn-e-invoice-receiver'), $product_name) . "\n";
        $message .= __('To continue using our services, please renew your subscription.', 'lhdn-e-invoice-receiver') . "\n\n";
        $message .= sprintf(__('You can renew by visiting: %s', 'lhdn-e-invoice-receiver'), wc_get_endpoint_url('subscriptions', '', wc_get_page_permalink('myaccount'))) . "\n\n";
        $message .= sprintf(__('Thank you for choosing %s.', 'lhdn-e-invoice-receiver'), get_bloginfo('name')) . "\n";
        
        wp_mail($user->user_email, $subject, $message);
    }
    
    /**
     * Send renewal reminder email
     * 
     * @param int $subscription_id The subscription ID
     */
    public function send_renewal_reminder($subscription_id) {
        $user_id = get_post_meta($subscription_id, '_leir_user_id', true);
        $status = get_post_meta($subscription_id, '_leir_status', true);
        
        // Only send reminder for active subscriptions
        if ($status !== 'active') {
            return;
        }
        
        $user = get_userdata($user_id);
        
        if (!$user) {
            return;
        }
        
        $product_id = get_post_meta($subscription_id, '_leir_product_id', true);
        $expiry_date = get_post_meta($subscription_id, '_leir_expiry_date', true);
        $product_name = '';
        
        if ($product_id) {
            $product = wc_get_product($product_id);
            if ($product) {
                $product_name = $product->get_name();
            }
        }
        
        $subject = sprintf(__('Your %s subscription will expire soon', 'lhdn-e-invoice-receiver'), get_bloginfo('name'));
        
        $message = sprintf(__('Hello %s,', 'lhdn-e-invoice-receiver'), $user->display_name) . "\n\n";
        $message .= sprintf(__('Your subscription to %s will expire on %s.', 'lhdn-e-invoice-receiver'), $product_name, date_i18n(get_option('date_format'), strtotime($expiry_date))) . "\n";
        $message .= __('To continue using our services without interruption, please renew your subscription before it expires.', 'lhdn-e-invoice-receiver') . "\n\n";
        $message .= sprintf(__('You can renew by visiting: %s', 'lhdn-e-invoice-receiver'), wc_get_endpoint_url('subscriptions', '', wc_get_page_permalink('myaccount'))) . "\n\n";
        $message .= sprintf(__('Thank you for choosing %s.', 'lhdn-e-invoice-receiver'), get_bloginfo('name')) . "\n";
        
        wp_mail($user->user_email, $subject, $message);
    }
}

// Initialize the subscription handler
new LEIR_Subscription_Handler();