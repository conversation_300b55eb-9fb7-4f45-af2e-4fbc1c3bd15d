=== LHDN E-Invoice Receiver ===
Contributors: yourname
Tags: e-invoice, lhdn, api, woocommerce
Requires at least: 5.0
Tested up to: 6.4
Stable tag: 1.0.0
Requires PHP: 7.4
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Receive e-invoice data from external sources with user-specific authorization tokens.

== Description ==

The LHDN E-Invoice Receiver plugin allows you to receive e-invoice data from external systems via a secure REST API endpoint. Each user has their own unique authentication token to secure their e-invoice submissions.

= Key Features =

* Secure REST API endpoint for receiving e-invoice data
* User-specific authorization tokens for secure API access
* Token management interface within WordPress user profiles
* Storage and management of received invoice data
* Debug mode for troubleshooting API interactions

= Use Cases =

This plugin is designed to work with the WooCommerce LHDN E-Invoice plugin, allowing external systems to submit e-invoice data to your WordPress site.

== Installation ==

1. Upload the `lhdn-e-invoice-receiver` folder to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Navigate to the 'E-Invoice' menu in the WordPress admin panel to configure settings
4. Generate API tokens for users who need to submit e-invoice data
5. Provide the API endpoint URL and token to external systems

== Frequently Asked Questions ==

= How do I generate an API token? =

API tokens can be generated from the user profile page. Navigate to Users > Your Profile (or edit another user's profile) and scroll down to the "E-Invoice API Tokens" section. Click the "Generate New Token" button to create a new token.

= What is the API endpoint URL? =

The API endpoint URL is displayed on the main E-Invoice dashboard page. It typically follows this format: `https://your-site.com/wp-json/lhdn-e-invoice-receiver/v1/receive-einvoice`

= How do I use the API endpoint? =

External systems should make a POST request to the API endpoint with the following:
- Headers:
  - `Content-Type: application/json`
  - `Authorization: Bearer YOUR_TOKEN` (replace YOUR_TOKEN with the generated token)
- Body: JSON array of invoice objects

= Can I delete an API token? =

Yes, you can delete API tokens from the user profile page. Navigate to the "E-Invoice API Tokens" section and click the "Delete" link next to the token you want to remove.

== Screenshots ==

1. E-Invoice dashboard
2. Token management interface
3. Invoice listing page

== Changelog ==

= 1.0.0 =
* Initial release

== Upgrade Notice ==

= 1.0.0 =
Initial release 