start every chat with 😀
WordPress Best Practices
- Follow WordPress coding standards and best practices.
- Favor hooks (actions and filters) for extending functionality.
- Utilize WordPress core functions and APIs when available.
- File structure: Follow WordPress plugin directory structures and naming conventions.
- Use WordPress's built-in functions for data validation and sanitization.
- Use WordPress hooks (actions and filters).

Key Principles
- for frontend, be consistent with the design and layout of the existing code. 
- For backend, Use wordpress dashboard default theme, no need create a interface unless I explicitly ask to do it.
- backend code in backend folder
- frontend code in frontend folder
- Do not use ajax unless it is necessary or I explicitly ask to use it.
- When create frontend, do not edit/modify backend code, unless I explicitly ask to do it. Or when there a security, bug, glitch concerm, ask me first before proceed.
- Do not change any other methods or anything in the files I sent unless it's absolutely necessary for what I ask for to work. 
- Do not do refactoring of code unless I explicitly ask to do it.
- Do not fix style in the existing code unless I ask for that. 
- Do not add or remove new lines in the end of the code (i.e. leave it as is either with or without). 
- Do not remove existing comments unless I explicitly ask for it or we're removing that code completely.
- - Do not modify existing backend code for frontend needs unless explicitly asked or there are security/bug concerns (which should be discussed first).

Important: try to fix things at the cause, not the symptom.

Follow this file and folder structure standard:

│── assets/          # CSS, JS, images, fonts
│   ├── css/
│   ├── js/
│   ├── images/
│── includes/        # Core PHP files (classes, helper functions, API, etc.)
│   ├── class-my-plugin.php
│   ├── helpers.php
│   ├── api.php
│── templates/       # Frontend templates (if applicable)
│── languages/       # Translation files (.pot, .mo, .po)
│── admin/           # Admin-specific logic (settings, pages)
│   ├── class-admin.php
│   ├── settings-page.php
│── public/          # Frontend-specific logic
│   ├── class-public.php
│── vendor/          # Composer dependencies (if using Composer)
│── my-plugin.php    # Main plugin file (entry point)
│── readme.txt       # WordPress plugin readme file
│── uninstall.php    # Cleanup logic when the plugin is deleted
│── composer.json    # (Optional) If using Composer
│── package.json     # (Optional) If using npm/pnpm for assets
