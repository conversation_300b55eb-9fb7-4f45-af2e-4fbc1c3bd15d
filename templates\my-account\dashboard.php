<?php
/**
 * My Account Dashboard Template
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

$current_user = wp_get_current_user();
?>

<div class="leir-my-account-dashboard">
    <h2><?php _e('Dashboard', 'lhdn-e-invoice-receiver'); ?></h2>
    
    <div class="leir-dashboard-welcome">
        <p>
            <?php
            printf(
                __('Hello %s (not %s? <a href="%s">Log out</a>)', 'lhdn-e-invoice-receiver'),
                '<strong>' . esc_html($current_user->display_name) . '</strong>',
                '<strong>' . esc_html($current_user->display_name) . '</strong>',
                esc_url(wp_logout_url(get_permalink()))
            );
            ?>
        </p>
    </div>

    <div class="leir-dashboard-content">
        <div class="row">
            <div class="col-md-6">
                <div class="leir-dashboard-section">
                    <h3><?php _e('Recent Orders', 'lhdn-e-invoice-receiver'); ?></h3>
                    <?php
                    // Get recent orders if WooCommerce is active
                    if (class_exists('WooCommerce')) {
                        $customer_orders = wc_get_orders(array(
                            'customer' => get_current_user_id(),
                            'limit' => 5,
                            'orderby' => 'date',
                            'order' => 'DESC',
                        ));

                        if (!empty($customer_orders)) {
                            echo '<table class="leir-recent-orders">';
                            echo '<thead><tr><th>' . __('Order', 'lhdn-e-invoice-receiver') . '</th><th>' . __('Date', 'lhdn-e-invoice-receiver') . '</th><th>' . __('Status', 'lhdn-e-invoice-receiver') . '</th><th>' . __('Total', 'lhdn-e-invoice-receiver') . '</th></tr></thead>';
                            echo '<tbody>';
                            
                            foreach ($customer_orders as $order) {
                                echo '<tr>';
                                echo '<td><a href="' . esc_url(add_query_arg('endpoint', 'orders', get_permalink())) . '">#' . $order->get_order_number() . '</a></td>';
                                echo '<td>' . wc_format_datetime($order->get_date_created()) . '</td>';
                                echo '<td>' . wc_get_order_status_name($order->get_status()) . '</td>';
                                echo '<td>' . $order->get_formatted_order_total() . '</td>';
                                echo '</tr>';
                            }
                            
                            echo '</tbody>';
                            echo '</table>';
                            
                            echo '<p><a href="' . esc_url(add_query_arg('endpoint', 'orders', get_permalink())) . '" class="button">' . __('View all orders', 'lhdn-e-invoice-receiver') . '</a></p>';
                        } else {
                            echo '<p>' . __('No orders found.', 'lhdn-e-invoice-receiver') . '</p>';
                        }
                    } else {
                        echo '<p>' . __('WooCommerce is not active.', 'lhdn-e-invoice-receiver') . '</p>';
                    }
                    ?>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="leir-dashboard-section">
                    <h3><?php _e('Recent E-Invoices', 'lhdn-e-invoice-receiver'); ?></h3>
                    <?php
                    // Get recent e-invoices
                    global $wpdb;
                    $table_name = $wpdb->prefix . 'leir_invoices';
                    $recent_invoices = $wpdb->get_results($wpdb->prepare(
                        "SELECT * FROM $table_name WHERE user_id = %d ORDER BY received_date DESC LIMIT 5",
                        get_current_user_id()
                    ));

                    if (!empty($recent_invoices)) {
                        echo '<table class="leir-recent-invoices">';
                        echo '<thead><tr><th>' . __('Transaction Code', 'lhdn-e-invoice-receiver') . '</th><th>' . __('Date', 'lhdn-e-invoice-receiver') . '</th></tr></thead>';
                        echo '<tbody>';
                        
                        foreach ($recent_invoices as $invoice) {
                            echo '<tr>';
                            echo '<td>' . esc_html($invoice->transaction_code) . '</td>';
                            echo '<td>' . esc_html(date_i18n(get_option('date_format'), strtotime($invoice->received_date))) . '</td>';
                            echo '</tr>';
                        }
                        
                        echo '</tbody>';
                        echo '</table>';
                        
                        echo '<p><a href="' . esc_url(add_query_arg('endpoint', 'e-invoice', get_permalink())) . '" class="button">' . __('View all e-invoices', 'lhdn-e-invoice-receiver') . '</a></p>';
                    } else {
                        echo '<p>' . __('No e-invoices found.', 'lhdn-e-invoice-receiver') . '</p>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>

    <div class="leir-dashboard-actions">
        <h3><?php _e('Account Actions', 'lhdn-e-invoice-receiver'); ?></h3>
        <div class="leir-action-buttons">
            <a href="<?php echo esc_url(add_query_arg('endpoint', 'edit-account', get_permalink())); ?>" class="button">
                <?php _e('Edit Account Details', 'lhdn-e-invoice-receiver'); ?>
            </a>
            <a href="<?php echo esc_url(add_query_arg('endpoint', 'e-invoice', get_permalink())); ?>" class="button">
                <?php _e('Manage E-Invoices', 'lhdn-e-invoice-receiver'); ?>
            </a>
        </div>
    </div>
</div> 