/**
 * Subscription styles
 */

/* Subscription table */
.woocommerce-MyAccount-subscriptions th,
.woocommerce-MyAccount-subscriptions td {
    padding: 1em 0.5em;
    text-align: left;
    vertical-align: middle;
}

.woocommerce-MyAccount-subscriptions .button {
    margin-right: 5px;
}

/* Subscription details on order page */
.subscription-data {
    margin: 2em 0;
    padding: 1em;
    border: 1px solid #eee;
    background-color: #f8f8f8;
    border-radius: 4px;
}

.subscription-data h3 {
    margin-top: 0;
}

/* Subscription meta box on product page */
#leir_subscription_meta_box .inside {
    padding: 12px;
}

#leir_subscription_meta_box label {
    display: inline-block;
    margin-bottom: 3px;
}

#leir_subscription_meta_box input[type="number"] {
    width: 80px;
}

/* Subscription status colors */
.subscription-status-active {
    color: #7ad03a;
}

.subscription-status-expired {
    color: #a00;
}

.subscription-status-pending {
    color: #ffba00;
}

/* Expiry date warning */
.subscription-expiry-warning {
    background-color: #fff8e5;
    border-left: 4px solid #ffb900;
    padding: 10px;
    margin: 1em 0;
}

/* Subscription renewal button */
.renew-subscription {
    background-color: #7ad03a;
    color: white;
}

.renew-subscription:hover {
    background-color: #69b02a;
    color: white;
}

/* Admin subscription list */
.subscription-list-table .column-status {
    width: 10%;
}

.subscription-list-table .column-user {
    width: 15%;
}

.subscription-list-table .column-product {
    width: 20%;
}

.subscription-list-table .column-dates {
    width: 25%;
}

.subscription-list-table .column-actions {
    width: 15%;
}

/* Subscription required page */
.subscription-plans {
    margin: 20px 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    list-style: none;
}

.subscription-plans li {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 20px;
    width: calc(33.33% - 20px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.subscription-plans li:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.subscription-plans h4 {
    margin-top: 0;
    font-size: 18px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.subscription-plans .price {
    font-size: 22px;
    font-weight: bold;
    color: #333;
    margin: 10px 0;
}

.subscription-plans .duration {
    color: #666;
    margin-bottom: 15px;
}

.subscription-plans .button {
    display: block;
    text-align: center;
    width: 100%;
}

@media screen and (max-width: 782px) {
    .woocommerce-MyAccount-subscriptions {
        display: block;
        overflow-x: auto;
    }
    
    .subscription-plans li {
        width: 100%;
    }
} 