/**
 * LHDN E-Invoice My Account Styles
 * Styles for the custom My Account page template
 */

/* My Account Wrapper */
.leir-my-account-wrapper {
    padding: 20px 0;
    background: #f8f9fa;
    min-height: 60vh;
}

.leir-my-account-wrapper .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Navigation */
.leir-my-account-navigation {
    background: #fff;
    border-radius: 8px;
    padding: 20px 20px 0 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 0;
}

.leir-my-account-navigation h2 {
    margin: 0 0 20px 0;
    font-size: 24px;
    color: #333;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}

/* Force horizontal navigation */.leir-my-account-navigation .leir-navigation ul {    list-style: none !important;    margin: 0 !important;    padding: 0 !important;    display: flex !important;    flex-direction: row !important;    flex-wrap: wrap !important;    gap: 2px !important;    border-bottom: none !important;}.leir-my-account-navigation .leir-navigation li {    margin: 0 !important;    border-bottom: none !important;    flex: 1 !important;    min-width: auto !important;    display: inline-block !important;}.leir-my-account-navigation .leir-navigation li:last-child {    border-bottom: none !important;}.leir-my-account-navigation .leir-navigation a {    display: block !important;    padding: 12px 20px !important;    color: #555 !important;    text-decoration: none !important;    transition: all 0.3s ease !important;    border-radius: 6px 6px 0 0 !important;    margin: 0 !important;    text-align: center !important;    border: 1px solid #ddd !important;    border-bottom: 1px solid #ddd !important;    position: relative !important;    background: #f8f9fa !important;    font-weight: 500 !important;}.leir-my-account-navigation .leir-navigation a:hover {    background: #e9ecef !important;    color: #0073aa !important;    text-decoration: none !important;    border-color: #0073aa !important;}.leir-my-account-navigation .leir-navigation li.active a {    background: #fff !important;    color: #0073aa !important;    border-color: #0073aa !important;    border-bottom-color: #fff !important;    font-weight: 600 !important;    position: relative !important;    z-index: 1 !important;    margin-bottom: -1px !important;}

/* Content Area */
.leir-my-account-content {
    background: #fff;
    border-radius: 0 0 8px 8px;
    padding: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    min-height: 400px;
    border-top: 1px solid #0073aa;
}

.leir-my-account-content h2 {
    margin: 0 0 25px 0;
    font-size: 28px;
    color: #333;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}

.leir-my-account-content h3 {
    margin: 20px 0 15px 0;
    font-size: 20px;
    color: #444;
}

/* Dashboard Styles */
.leir-dashboard-welcome {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 25px;
}

.leir-dashboard-welcome p {
    margin: 0;
    color: #0073aa;
}

.leir-dashboard-section {
    background: #f9f9f9;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e5e5e5;
}

.leir-dashboard-section h3 {
    margin-top: 0;
    color: #333;
    border-bottom: 1px solid #ddd;
    padding-bottom: 8px;
}

.leir-action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Grid System for Dashboard */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 15px;
}

/* Tables */
.leir-recent-orders,
.leir-recent-invoices,
.leir-orders-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    background: #fff;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.leir-recent-orders th,
.leir-recent-invoices th,
.leir-orders-table th {
    background: #f8f9fa;
    color: #333;
    font-weight: 600;
    padding: 12px 15px;
    text-align: left;
    border-bottom: 2px solid #dee2e6;
}

.leir-recent-orders td,
.leir-recent-invoices td,
.leir-orders-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
}

.leir-recent-orders tr:hover,
.leir-recent-invoices tr:hover,
.leir-orders-table tr:hover {
    background: #f8f9fa;
}

.leir-orders-table-wrapper {
    overflow-x: auto;
    margin: 20px 0;
}

/* Order Status */
.order-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.order-status.status-pending {
    background: #fff3cd;
    color: #856404;
}

.order-status.status-processing {
    background: #d1ecf1;
    color: #0c5460;
}

.order-status.status-on-hold {
    background: #f8d7da;
    color: #721c24;
}

.order-status.status-completed {
    background: #d4edda;
    color: #155724;
}

.order-status.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.order-status.status-refunded {
    background: #e2e3e5;
    color: #383d41;
}

.order-status.status-failed {
    background: #f8d7da;
    color: #721c24;
}

/* Buttons */
.button,
.leir-button {
    display: inline-block;
    padding: 8px 16px;
    background: #0073aa;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s ease;
    margin-right: 5px;
    margin-bottom: 5px;
}

.button:hover,
.leir-button:hover {
    background: #005a87;
    color: #fff;
    text-decoration: none;
}

.button.view-order {
    background: #28a745;
}

.button.view-order:hover {
    background: #218838;
}

.button.pay-order {
    background: #ffc107;
    color: #212529;
}

.button.pay-order:hover {
    background: #e0a800;
    color: #212529;
}

.button.cancel-order {
    background: #dc3545;
}

.button.cancel-order:hover {
    background: #c82333;
}

/* Forms */
.leir-edit-account-form {
    max-width: 600px;
}

.leir-form-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px 20px -10px;
}

.leir-form-group {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 10px;
}

.leir-form-group-wide {
    flex: 0 0 100%;
    max-width: 100%;
}

.leir-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.leir-form-group .required {
    color: #dc3545;
}

.leir-form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.leir-form-control:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.2);
}

.leir-form-group small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 12px;
}

.leir-password-fieldset {
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    margin: 20px 0;
}

.leir-password-fieldset legend {
    padding: 0 10px;
    font-weight: 600;
    color: #333;
}

/* Notices */
.leir-notice {
    padding: 12px 15px;
    margin: 20px 0;
    border-radius: 4px;
    border-left: 4px solid;
}

.leir-notice p {
    margin: 0;
}

.leir-notice-success {
    background: #d4edda;
    border-left-color: #28a745;
    color: #155724;
}

.leir-notice-error {
    background: #f8d7da;
    border-left-color: #dc3545;
    color: #721c24;
}

/* No Orders/Content */
.leir-no-orders {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.leir-no-orders p {
    font-size: 16px;
    margin-bottom: 20px;
}

/* Pagination */
.leir-pagination {
    text-align: center;
    margin: 30px 0;
}

.leir-pagination .page-numbers {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    background: #fff;
    border: 1px solid #ddd;
    color: #0073aa;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.leir-pagination .page-numbers:hover,
.leir-pagination .page-numbers.current {
    background: #0073aa;
    color: #fff;
    border-color: #0073aa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .leir-my-account-navigation {
        margin-bottom: 0;
        padding: 15px 15px 0 15px;
    }
    
    .leir-navigation ul {
        display: flex;
        flex-wrap: wrap;
        gap: 2px;
    }
    
    .leir-navigation li {
        border-bottom: none;
        flex: 1;
        min-width: calc(50% - 1px);
    }
    
    .leir-navigation a {
        text-align: center;
        padding: 10px 8px;
        font-size: 14px;
    }
    
    .leir-my-account-content {
        padding: 20px;
    }
    
    .leir-form-group {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .leir-action-buttons {
        flex-direction: column;
    }
    
    .leir-orders-table-wrapper {
        font-size: 14px;
    }
    
    .leir-orders-table th,
    .leir-orders-table td {
        padding: 8px 6px;
    }
}

@media (max-width: 480px) {
    .leir-my-account-wrapper {
        padding: 10px 0;
    }
    
    .leir-my-account-wrapper .container {
        padding: 0 10px;
    }
    
    .leir-my-account-navigation,
    .leir-my-account-content {
        padding: 15px;
    }
    
    .leir-navigation li {
        min-width: 100%;
    }
    
    .leir-orders-table th:nth-child(3),
    .leir-orders-table td:nth-child(3) {
        display: none;
    }
} 