<?php
/**
 * Template Name: My Account
 * Template Post Type: page
 * Description: A template that replicates WooCommerce My Account functionality
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

get_header();

/**
 * Fires before the My Account page content
 */
do_action('leir_before_my_account');

?>
<div class="leir-my-account-wrapper">
    <div class="container">
        <div class="leir-my-account-navigation">
            <h2><?php _e('My Account', 'lhdn-e-invoice-receiver'); ?></h2>
            <nav class="leir-navigation">
                <ul style="display: flex !important; list-style: none !important; margin: 0 !important; padding: 0 !important; gap: 2px !important;">
                            <?php
                            // Define navigation items
                            $navigation_items = apply_filters('leir_my_account_navigation_items', array(
                                'dashboard' => array(
                                    'label' => __('Dashboard', 'lhdn-e-invoice-receiver'),
                                    'endpoint' => '',
                                ),
                                'orders' => array(
                                    'label' => __('Orders', 'lhdn-e-invoice-receiver'),
                                    'endpoint' => 'orders',
                                ),
                                'e-invoice' => array(
                                    'label' => __('E-Invoices', 'lhdn-e-invoice-receiver'),
                                    'endpoint' => 'e-invoice',
                                ),
                                'edit-account' => array(
                                    'label' => __('Account details', 'lhdn-e-invoice-receiver'),
                                    'endpoint' => 'edit-account',
                                ),
                                'customer-logout' => array(
                                    'label' => __('Logout', 'lhdn-e-invoice-receiver'),
                                    'endpoint' => 'customer-logout',
                                ),
                            ));

                            // Get current endpoint
                            $current_endpoint = isset($_GET['endpoint']) ? sanitize_text_field($_GET['endpoint']) : '';
                            if (empty($current_endpoint)) {
                                $current_endpoint = 'dashboard';
                            }

                            // Output navigation items
                            foreach ($navigation_items as $key => $item) {
                                $classes = $key === $current_endpoint ? 'active' : '';
                                $url = add_query_arg('endpoint', $item['endpoint'], get_permalink());
                                if ($key === 'dashboard') {
                                    $url = get_permalink();
                                } elseif ($key === 'customer-logout') {
                                    $url = wp_logout_url(get_permalink());
                                }
                                
                                echo '<li class="' . esc_attr($classes) . '" style="flex: 1; margin: 0; border-bottom: none; display: inline-block;">';
                                echo '<a href="' . esc_url($url) . '" style="display: block; padding: 12px 20px; text-align: center; text-decoration: none; border-radius: 6px 6px 0 0; border: 1px solid #ddd; background: ' . ($key === $current_endpoint ? '#fff' : '#f8f9fa') . '; color: ' . ($key === $current_endpoint ? '#0073aa' : '#555') . ';">' . esc_html($item['label']) . '</a>';
                                echo '</li>';
                            }
                            ?>
                </ul>
            </nav>
        </div>
        <div class="leir-my-account-content">
                    <?php
                    // Handle different endpoints
                    switch ($current_endpoint) {
                        case 'orders':
                            include_once LEIR_PLUGIN_DIR . 'templates/my-account/orders.php';
                            break;
                            
                        case 'e-invoice':
                            if (class_exists('LEIR_Public')) {
                                $public = new LEIR_Public();
                                $public->einvoice_tab_content();
                            } else {
                                echo '<p>' . __('E-Invoice functionality is not available.', 'lhdn-e-invoice-receiver') . '</p>';
                            }
                            break;
                            
                        case 'edit-account':
                            include_once LEIR_PLUGIN_DIR . 'templates/my-account/edit-account.php';
                            break;
                            
                        case 'dashboard':
                        default:
                            include_once LEIR_PLUGIN_DIR . 'templates/my-account/dashboard.php';
                            break;
                    }
                    ?>
        </div>
    </div>
</div>

<?php
/**
 * Fires after the My Account page content
 */
do_action('leir_after_my_account');

get_footer(); 